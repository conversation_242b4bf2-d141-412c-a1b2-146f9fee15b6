#[cfg(feature = "Win32_UI_Accessibility")]
#[doc = "Required features: `\"Win32_UI_Accessibility\"`"]
pub mod Accessibility;
#[cfg(feature = "Win32_UI_ColorSystem")]
#[doc = "Required features: `\"Win32_UI_ColorSystem\"`"]
pub mod ColorSystem;
#[cfg(feature = "Win32_UI_Controls")]
#[doc = "Required features: `\"Win32_UI_Controls\"`"]
pub mod Controls;
#[cfg(feature = "Win32_UI_HiDpi")]
#[doc = "Required features: `\"Win32_UI_HiDpi\"`"]
pub mod HiDpi;
#[cfg(feature = "Win32_UI_Input")]
#[doc = "Required features: `\"Win32_UI_Input\"`"]
pub mod Input;
#[cfg(feature = "Win32_UI_InteractionContext")]
#[doc = "Required features: `\"Win32_UI_InteractionContext\"`"]
pub mod InteractionContext;
#[cfg(feature = "Win32_UI_Magnification")]
#[doc = "Required features: `\"Win32_UI_Magnification\"`"]
pub mod Magnification;
#[cfg(feature = "Win32_UI_Shell")]
#[doc = "Required features: `\"Win32_UI_Shell\"`"]
pub mod Shell;
#[cfg(feature = "Win32_UI_TabletPC")]
#[doc = "Required features: `\"Win32_UI_TabletPC\"`"]
pub mod TabletPC;
#[cfg(feature = "Win32_UI_TextServices")]
#[doc = "Required features: `\"Win32_UI_TextServices\"`"]
pub mod TextServices;
#[cfg(feature = "Win32_UI_WindowsAndMessaging")]
#[doc = "Required features: `\"Win32_UI_WindowsAndMessaging\"`"]
pub mod WindowsAndMessaging;
