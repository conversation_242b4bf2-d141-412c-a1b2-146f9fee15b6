#[cfg(feature = "Win32_Data")]
#[doc = "Required features: `\"Win32_Data\"`"]
pub mod Data;
#[cfg(feature = "Win32_Devices")]
#[doc = "Required features: `\"Win32_Devices\"`"]
pub mod Devices;
#[cfg(feature = "Win32_Foundation")]
#[doc = "Required features: `\"Win32_Foundation\"`"]
pub mod Foundation;
#[cfg(feature = "Win32_Gaming")]
#[doc = "Required features: `\"Win32_Gaming\"`"]
pub mod Gaming;
#[cfg(feature = "Win32_Globalization")]
#[doc = "Required features: `\"Win32_Globalization\"`"]
pub mod Globalization;
#[cfg(feature = "Win32_Graphics")]
#[doc = "Required features: `\"Win32_Graphics\"`"]
pub mod Graphics;
#[cfg(feature = "Win32_Management")]
#[doc = "Required features: `\"Win32_Management\"`"]
pub mod Management;
#[cfg(feature = "Win32_Media")]
#[doc = "Required features: `\"Win32_Media\"`"]
pub mod Media;
#[cfg(feature = "Win32_NetworkManagement")]
#[doc = "Required features: `\"Win32_NetworkManagement\"`"]
pub mod NetworkManagement;
#[cfg(feature = "Win32_Networking")]
#[doc = "Required features: `\"Win32_Networking\"`"]
pub mod Networking;
#[cfg(feature = "Win32_Security")]
#[doc = "Required features: `\"Win32_Security\"`"]
pub mod Security;
#[cfg(feature = "Win32_Storage")]
#[doc = "Required features: `\"Win32_Storage\"`"]
pub mod Storage;
#[cfg(feature = "Win32_System")]
#[doc = "Required features: `\"Win32_System\"`"]
pub mod System;
#[cfg(feature = "Win32_UI")]
#[doc = "Required features: `\"Win32_UI\"`"]
pub mod UI;
#[cfg(feature = "Win32_Web")]
#[doc = "Required features: `\"Win32_Web\"`"]
pub mod Web;
