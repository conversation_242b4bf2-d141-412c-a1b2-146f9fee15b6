#[cfg(feature = "Win32_Graphics_Gdi")]
::windows_targets::link!("imgutil.dll" "system" #[doc = "Required features: `\"Win32_Graphics_Gdi\"`"] fn ComputeInvCMAP(prgbcolors : *const super::super::Graphics::Gdi:: RGBQUAD, ncolors : u32, pinvtable : *mut u8, cbtable : u32) -> ::windows_sys::core::HRESULT);
#[cfg(all(feature = "Win32_Graphics_DirectDraw", feature = "Win32_Graphics_Gdi"))]
::windows_targets::link!("imgutil.dll" "system" #[doc = "Required features: `\"Win32_Graphics_DirectDraw\"`, `\"Win32_Graphics_Gdi\"`"] fn CreateDDrawSurfaceOnDIB(hbmdib : super::super::Graphics::Gdi:: HBITMAP, ppsurface : *mut super::super::Graphics::DirectDraw:: IDirectDrawSurface) -> ::windows_sys::core::HRESULT);
::windows_targets::link!("imgutil.dll" "system" fn CreateMIMEMap(ppmap : *mut IMapMIMEToCLSID) -> ::windows_sys::core::HRESULT);
#[cfg(feature = "Win32_System_Com")]
::windows_targets::link!("imgutil.dll" "system" #[doc = "Required features: `\"Win32_System_Com\"`"] fn DecodeImage(pstream : super::super::System::Com:: IStream, pmap : IMapMIMEToCLSID, peventsink : ::windows_sys::core::IUnknown) -> ::windows_sys::core::HRESULT);
#[cfg(feature = "Win32_System_Com")]
::windows_targets::link!("imgutil.dll" "system" #[doc = "Required features: `\"Win32_System_Com\"`"] fn DecodeImageEx(pstream : super::super::System::Com:: IStream, pmap : IMapMIMEToCLSID, peventsink : ::windows_sys::core::IUnknown, pszmimetypeparam : ::windows_sys::core::PCWSTR) -> ::windows_sys::core::HRESULT);
#[cfg(feature = "Win32_Graphics_Gdi")]
::windows_targets::link!("imgutil.dll" "system" #[doc = "Required features: `\"Win32_Graphics_Gdi\"`"] fn DitherTo8(pdestbits : *mut u8, ndestpitch : i32, psrcbits : *mut u8, nsrcpitch : i32, bfidsrc : *const ::windows_sys::core::GUID, prgbdestcolors : *mut super::super::Graphics::Gdi:: RGBQUAD, prgbsrccolors : *mut super::super::Graphics::Gdi:: RGBQUAD, pbdestinvmap : *mut u8, x : i32, y : i32, cx : i32, cy : i32, ldesttrans : i32, lsrctrans : i32) -> ::windows_sys::core::HRESULT);
::windows_targets::link!("imgutil.dll" "system" fn GetMaxMIMEIDBytes(pnmaxbytes : *mut u32) -> ::windows_sys::core::HRESULT);
::windows_targets::link!("ieframe.dll" "system" fn IEAssociateThreadWithTab(dwtabthreadid : u32, dwassociatedthreadid : u32) -> ::windows_sys::core::HRESULT);
#[cfg(feature = "Win32_Foundation")]
::windows_targets::link!("ieframe.dll" "system" #[doc = "Required features: `\"Win32_Foundation\"`"] fn IECancelSaveFile(hstate : super::super::Foundation:: HANDLE) -> ::windows_sys::core::HRESULT);
#[cfg(all(feature = "Win32_Foundation", feature = "Win32_Security"))]
::windows_targets::link!("ieframe.dll" "system" #[doc = "Required features: `\"Win32_Foundation\"`, `\"Win32_Security\"`"] fn IECreateDirectory(lppathname : ::windows_sys::core::PCWSTR, lpsecurityattributes : *const super::super::Security:: SECURITY_ATTRIBUTES) -> super::super::Foundation:: BOOL);
#[cfg(all(feature = "Win32_Foundation", feature = "Win32_Security"))]
::windows_targets::link!("ieframe.dll" "system" #[doc = "Required features: `\"Win32_Foundation\"`, `\"Win32_Security\"`"] fn IECreateFile(lpfilename : ::windows_sys::core::PCWSTR, dwdesiredaccess : u32, dwsharemode : u32, lpsecurityattributes : *const super::super::Security:: SECURITY_ATTRIBUTES, dwcreationdisposition : u32, dwflagsandattributes : u32, htemplatefile : super::super::Foundation:: HANDLE) -> super::super::Foundation:: HANDLE);
#[cfg(feature = "Win32_Foundation")]
::windows_targets::link!("ieframe.dll" "system" #[doc = "Required features: `\"Win32_Foundation\"`"] fn IEDeleteFile(lpfilename : ::windows_sys::core::PCWSTR) -> super::super::Foundation:: BOOL);
::windows_targets::link!("ieframe.dll" "system" fn IEDisassociateThreadWithTab(dwtabthreadid : u32, dwassociatedthreadid : u32) -> ::windows_sys::core::HRESULT);
#[cfg(all(feature = "Win32_Foundation", feature = "Win32_Storage_FileSystem"))]
::windows_targets::link!("ieframe.dll" "system" #[doc = "Required features: `\"Win32_Foundation\"`, `\"Win32_Storage_FileSystem\"`"] fn IEFindFirstFile(lpfilename : ::windows_sys::core::PCWSTR, lpfindfiledata : *const super::super::Storage::FileSystem:: WIN32_FIND_DATAA) -> super::super::Foundation:: HANDLE);
#[cfg(all(feature = "Win32_Foundation", feature = "Win32_Storage_FileSystem"))]
::windows_targets::link!("ieframe.dll" "system" #[doc = "Required features: `\"Win32_Foundation\"`, `\"Win32_Storage_FileSystem\"`"] fn IEGetFileAttributesEx(lpfilename : ::windows_sys::core::PCWSTR, finfolevelid : super::super::Storage::FileSystem:: GET_FILEEX_INFO_LEVELS, lpfileinformation : *const ::core::ffi::c_void) -> super::super::Foundation:: BOOL);
::windows_targets::link!("ieframe.dll" "system" fn IEGetProtectedModeCookie(lpszurl : ::windows_sys::core::PCWSTR, lpszcookiename : ::windows_sys::core::PCWSTR, lpszcookiedata : ::windows_sys::core::PWSTR, pcchcookiedata : *mut u32, dwflags : u32) -> ::windows_sys::core::HRESULT);
::windows_targets::link!("ieframe.dll" "system" fn IEGetWriteableFolderPath(clsidfolderid : *const ::windows_sys::core::GUID, lppwstrpath : *mut ::windows_sys::core::PWSTR) -> ::windows_sys::core::HRESULT);
#[cfg(feature = "Win32_System_Registry")]
::windows_targets::link!("ieframe.dll" "system" #[doc = "Required features: `\"Win32_System_Registry\"`"] fn IEGetWriteableLowHKCU(phkey : *mut super::super::System::Registry:: HKEY) -> ::windows_sys::core::HRESULT);
#[cfg(feature = "Win32_Foundation")]
::windows_targets::link!("ieframe.dll" "system" #[doc = "Required features: `\"Win32_Foundation\"`"] fn IEInPrivateFilteringEnabled() -> super::super::Foundation:: BOOL);
#[cfg(feature = "Win32_Foundation")]
::windows_targets::link!("ieframe.dll" "system" #[doc = "Required features: `\"Win32_Foundation\"`"] fn IEIsInPrivateBrowsing() -> super::super::Foundation:: BOOL);
#[cfg(feature = "Win32_Foundation")]
::windows_targets::link!("ieframe.dll" "system" #[doc = "Required features: `\"Win32_Foundation\"`"] fn IEIsProtectedModeProcess(pbresult : *mut super::super::Foundation:: BOOL) -> ::windows_sys::core::HRESULT);
::windows_targets::link!("ieframe.dll" "system" fn IEIsProtectedModeURL(lpwstrurl : ::windows_sys::core::PCWSTR) -> ::windows_sys::core::HRESULT);
#[cfg(all(feature = "Win32_Foundation", feature = "Win32_System_Threading"))]
::windows_targets::link!("ieframe.dll" "system" #[doc = "Required features: `\"Win32_Foundation\"`, `\"Win32_System_Threading\"`"] fn IELaunchURL(lpwstrurl : ::windows_sys::core::PCWSTR, lpprocinfo : *mut super::super::System::Threading:: PROCESS_INFORMATION, lpinfo : *const ::core::ffi::c_void) -> ::windows_sys::core::HRESULT);
#[cfg(feature = "Win32_Foundation")]
::windows_targets::link!("ieframe.dll" "system" #[doc = "Required features: `\"Win32_Foundation\"`"] fn IEMoveFileEx(lpexistingfilename : ::windows_sys::core::PCWSTR, lpnewfilename : ::windows_sys::core::PCWSTR, dwflags : u32) -> super::super::Foundation:: BOOL);
::windows_targets::link!("ieframe.dll" "system" fn IERefreshElevationPolicy() -> ::windows_sys::core::HRESULT);
#[cfg(all(feature = "Win32_Foundation", feature = "Win32_Security", feature = "Win32_System_Registry"))]
::windows_targets::link!("ieframe.dll" "system" #[doc = "Required features: `\"Win32_Foundation\"`, `\"Win32_Security\"`, `\"Win32_System_Registry\"`"] fn IERegCreateKeyEx(lpsubkey : ::windows_sys::core::PCWSTR, reserved : u32, lpclass : ::windows_sys::core::PCWSTR, dwoptions : u32, samdesired : u32, lpsecurityattributes : *const super::super::Security:: SECURITY_ATTRIBUTES, phkresult : *mut super::super::System::Registry:: HKEY, lpdwdisposition : *mut u32) -> ::windows_sys::core::HRESULT);
::windows_targets::link!("ieframe.dll" "system" fn IERegSetValueEx(lpsubkey : ::windows_sys::core::PCWSTR, lpvaluename : ::windows_sys::core::PCWSTR, reserved : u32, dwtype : u32, lpdata : *const u8, cbdata : u32) -> ::windows_sys::core::HRESULT);
#[cfg(feature = "Win32_Foundation")]
::windows_targets::link!("ieframe.dll" "system" #[doc = "Required features: `\"Win32_Foundation\"`"] fn IERegisterWritableRegistryKey(guid : ::windows_sys::core::GUID, lpsubkey : ::windows_sys::core::PCWSTR, fsubkeyallowed : super::super::Foundation:: BOOL) -> ::windows_sys::core::HRESULT);
::windows_targets::link!("ieframe.dll" "system" fn IERegisterWritableRegistryValue(guid : ::windows_sys::core::GUID, lppath : ::windows_sys::core::PCWSTR, lpvaluename : ::windows_sys::core::PCWSTR, dwtype : u32, lpdata : *const u8, cbmaxdata : u32) -> ::windows_sys::core::HRESULT);
#[cfg(feature = "Win32_Foundation")]
::windows_targets::link!("ieframe.dll" "system" #[doc = "Required features: `\"Win32_Foundation\"`"] fn IERemoveDirectory(lppathname : ::windows_sys::core::PCWSTR) -> super::super::Foundation:: BOOL);
#[cfg(feature = "Win32_Foundation")]
::windows_targets::link!("ieframe.dll" "system" #[doc = "Required features: `\"Win32_Foundation\"`"] fn IESaveFile(hstate : super::super::Foundation:: HANDLE, lpwstrsourcefile : ::windows_sys::core::PCWSTR) -> ::windows_sys::core::HRESULT);
::windows_targets::link!("ieframe.dll" "system" fn IESetProtectedModeCookie(lpszurl : ::windows_sys::core::PCWSTR, lpszcookiename : ::windows_sys::core::PCWSTR, lpszcookiedata : ::windows_sys::core::PCWSTR, dwflags : u32) -> ::windows_sys::core::HRESULT);
#[cfg(feature = "Win32_Foundation")]
::windows_targets::link!("ieframe.dll" "system" #[doc = "Required features: `\"Win32_Foundation\"`"] fn IEShowOpenFileDialog(hwnd : super::super::Foundation:: HWND, lpwstrfilename : ::windows_sys::core::PWSTR, cchmaxfilename : u32, lpwstrinitialdir : ::windows_sys::core::PCWSTR, lpwstrfilter : ::windows_sys::core::PCWSTR, lpwstrdefext : ::windows_sys::core::PCWSTR, dwfilterindex : u32, dwflags : u32, phfile : *mut super::super::Foundation:: HANDLE) -> ::windows_sys::core::HRESULT);
#[cfg(feature = "Win32_Foundation")]
::windows_targets::link!("ieframe.dll" "system" #[doc = "Required features: `\"Win32_Foundation\"`"] fn IEShowSaveFileDialog(hwnd : super::super::Foundation:: HWND, lpwstrinitialfilename : ::windows_sys::core::PCWSTR, lpwstrinitialdir : ::windows_sys::core::PCWSTR, lpwstrfilter : ::windows_sys::core::PCWSTR, lpwstrdefext : ::windows_sys::core::PCWSTR, dwfilterindex : u32, dwflags : u32, lppwstrdestinationfilepath : *mut ::windows_sys::core::PWSTR, phstate : *mut super::super::Foundation:: HANDLE) -> ::windows_sys::core::HRESULT);
#[cfg(feature = "Win32_Foundation")]
::windows_targets::link!("ieframe.dll" "system" #[doc = "Required features: `\"Win32_Foundation\"`"] fn IETrackingProtectionEnabled() -> super::super::Foundation:: BOOL);
::windows_targets::link!("ieframe.dll" "system" fn IEUnregisterWritableRegistry(guid : ::windows_sys::core::GUID) -> ::windows_sys::core::HRESULT);
::windows_targets::link!("imgutil.dll" "system" fn IdentifyMIMEType(pbbytes : *const u8, nbytes : u32, pnformat : *mut u32) -> ::windows_sys::core::HRESULT);
#[cfg(feature = "Win32_Foundation")]
::windows_targets::link!("msrating.dll" "system" #[doc = "Required features: `\"Win32_Foundation\"`"] fn RatingAccessDeniedDialog(hdlg : super::super::Foundation:: HWND, pszusername : ::windows_sys::core::PCSTR, pszcontentdescription : ::windows_sys::core::PCSTR, pratingdetails : *mut ::core::ffi::c_void) -> ::windows_sys::core::HRESULT);
#[cfg(feature = "Win32_Foundation")]
::windows_targets::link!("msrating.dll" "system" #[doc = "Required features: `\"Win32_Foundation\"`"] fn RatingAccessDeniedDialog2(hdlg : super::super::Foundation:: HWND, pszusername : ::windows_sys::core::PCSTR, pratingdetails : *mut ::core::ffi::c_void) -> ::windows_sys::core::HRESULT);
#[cfg(feature = "Win32_Foundation")]
::windows_targets::link!("msrating.dll" "system" #[doc = "Required features: `\"Win32_Foundation\"`"] fn RatingAccessDeniedDialog2W(hdlg : super::super::Foundation:: HWND, pszusername : ::windows_sys::core::PCWSTR, pratingdetails : *mut ::core::ffi::c_void) -> ::windows_sys::core::HRESULT);
#[cfg(feature = "Win32_Foundation")]
::windows_targets::link!("msrating.dll" "system" #[doc = "Required features: `\"Win32_Foundation\"`"] fn RatingAccessDeniedDialogW(hdlg : super::super::Foundation:: HWND, pszusername : ::windows_sys::core::PCWSTR, pszcontentdescription : ::windows_sys::core::PCWSTR, pratingdetails : *mut ::core::ffi::c_void) -> ::windows_sys::core::HRESULT);
#[cfg(feature = "Win32_Foundation")]
::windows_targets::link!("msrating.dll" "system" #[doc = "Required features: `\"Win32_Foundation\"`"] fn RatingAddToApprovedSites(hdlg : super::super::Foundation:: HWND, cbpasswordblob : u32, pbpasswordblob : *mut u8, lpszurl : ::windows_sys::core::PCWSTR, falwaysnever : super::super::Foundation:: BOOL, fsitepage : super::super::Foundation:: BOOL, fapprovedsitesenforced : super::super::Foundation:: BOOL) -> ::windows_sys::core::HRESULT);
::windows_targets::link!("msrating.dll" "system" fn RatingCheckUserAccess(pszusername : ::windows_sys::core::PCSTR, pszurl : ::windows_sys::core::PCSTR, pszratinginfo : ::windows_sys::core::PCSTR, pdata : *const u8, cbdata : u32, ppratingdetails : *mut *mut ::core::ffi::c_void) -> ::windows_sys::core::HRESULT);
::windows_targets::link!("msrating.dll" "system" fn RatingCheckUserAccessW(pszusername : ::windows_sys::core::PCWSTR, pszurl : ::windows_sys::core::PCWSTR, pszratinginfo : ::windows_sys::core::PCWSTR, pdata : *const u8, cbdata : u32, ppratingdetails : *mut *mut ::core::ffi::c_void) -> ::windows_sys::core::HRESULT);
#[cfg(feature = "Win32_Foundation")]
::windows_targets::link!("msrating.dll" "system" #[doc = "Required features: `\"Win32_Foundation\"`"] fn RatingClickedOnPRFInternal(hwndowner : super::super::Foundation:: HWND, param1 : super::super::Foundation:: HINSTANCE, lpszfilename : ::windows_sys::core::PCSTR, nshow : i32) -> ::windows_sys::core::HRESULT);
#[cfg(feature = "Win32_Foundation")]
::windows_targets::link!("msrating.dll" "system" #[doc = "Required features: `\"Win32_Foundation\"`"] fn RatingClickedOnRATInternal(hwndowner : super::super::Foundation:: HWND, param1 : super::super::Foundation:: HINSTANCE, lpszfilename : ::windows_sys::core::PCSTR, nshow : i32) -> ::windows_sys::core::HRESULT);
#[cfg(feature = "Win32_Foundation")]
::windows_targets::link!("msrating.dll" "system" #[doc = "Required features: `\"Win32_Foundation\"`"] fn RatingEnable(hwndparent : super::super::Foundation:: HWND, pszusername : ::windows_sys::core::PCSTR, fenable : super::super::Foundation:: BOOL) -> ::windows_sys::core::HRESULT);
#[cfg(feature = "Win32_Foundation")]
::windows_targets::link!("msrating.dll" "system" #[doc = "Required features: `\"Win32_Foundation\"`"] fn RatingEnableW(hwndparent : super::super::Foundation:: HWND, pszusername : ::windows_sys::core::PCWSTR, fenable : super::super::Foundation:: BOOL) -> ::windows_sys::core::HRESULT);
::windows_targets::link!("msrating.dll" "system" fn RatingEnabledQuery() -> ::windows_sys::core::HRESULT);
::windows_targets::link!("msrating.dll" "system" fn RatingFreeDetails(pratingdetails : *const ::core::ffi::c_void) -> ::windows_sys::core::HRESULT);
::windows_targets::link!("msrating.dll" "system" fn RatingInit() -> ::windows_sys::core::HRESULT);
#[cfg(feature = "Win32_Foundation")]
::windows_targets::link!("msrating.dll" "system" #[doc = "Required features: `\"Win32_Foundation\"`"] fn RatingObtainCancel(hratingobtainquery : super::super::Foundation:: HANDLE) -> ::windows_sys::core::HRESULT);
#[cfg(feature = "Win32_Foundation")]
::windows_targets::link!("msrating.dll" "system" #[doc = "Required features: `\"Win32_Foundation\"`"] fn RatingObtainQuery(psztargeturl : ::windows_sys::core::PCSTR, dwuserdata : u32, fcallback : isize, phratingobtainquery : *mut super::super::Foundation:: HANDLE) -> ::windows_sys::core::HRESULT);
#[cfg(feature = "Win32_Foundation")]
::windows_targets::link!("msrating.dll" "system" #[doc = "Required features: `\"Win32_Foundation\"`"] fn RatingObtainQueryW(psztargeturl : ::windows_sys::core::PCWSTR, dwuserdata : u32, fcallback : isize, phratingobtainquery : *mut super::super::Foundation:: HANDLE) -> ::windows_sys::core::HRESULT);
#[cfg(feature = "Win32_Foundation")]
::windows_targets::link!("msrating.dll" "system" #[doc = "Required features: `\"Win32_Foundation\"`"] fn RatingSetupUI(hdlg : super::super::Foundation:: HWND, pszusername : ::windows_sys::core::PCSTR) -> ::windows_sys::core::HRESULT);
#[cfg(feature = "Win32_Foundation")]
::windows_targets::link!("msrating.dll" "system" #[doc = "Required features: `\"Win32_Foundation\"`"] fn RatingSetupUIW(hdlg : super::super::Foundation:: HWND, pszusername : ::windows_sys::core::PCWSTR) -> ::windows_sys::core::HRESULT);
#[cfg(feature = "Win32_System_Com")]
::windows_targets::link!("imgutil.dll" "system" #[doc = "Required features: `\"Win32_System_Com\"`"] fn SniffStream(pinstream : super::super::System::Com:: IStream, pnformat : *mut u32, ppoutstream : *mut super::super::System::Com:: IStream) -> ::windows_sys::core::HRESULT);
pub type IActiveXUIHandlerSite = *mut ::core::ffi::c_void;
pub type IActiveXUIHandlerSite2 = *mut ::core::ffi::c_void;
pub type IActiveXUIHandlerSite3 = *mut ::core::ffi::c_void;
pub type IAnchorClick = *mut ::core::ffi::c_void;
pub type IAudioSessionSite = *mut ::core::ffi::c_void;
pub type ICaretPositionProvider = *mut ::core::ffi::c_void;
pub type IDeviceRect = *mut ::core::ffi::c_void;
pub type IDithererImpl = *mut ::core::ffi::c_void;
pub type IDocObjectService = *mut ::core::ffi::c_void;
pub type IDownloadBehavior = *mut ::core::ffi::c_void;
pub type IDownloadManager = *mut ::core::ffi::c_void;
pub type IEnumManagerFrames = *mut ::core::ffi::c_void;
pub type IEnumOpenServiceActivity = *mut ::core::ffi::c_void;
pub type IEnumOpenServiceActivityCategory = *mut ::core::ffi::c_void;
pub type IEnumSTATURL = *mut ::core::ffi::c_void;
pub type IExtensionValidation = *mut ::core::ffi::c_void;
pub type IHTMLPersistData = *mut ::core::ffi::c_void;
pub type IHTMLPersistDataOM = *mut ::core::ffi::c_void;
pub type IHTMLUserDataOM = *mut ::core::ffi::c_void;
pub type IHeaderFooter = *mut ::core::ffi::c_void;
pub type IHeaderFooter2 = *mut ::core::ffi::c_void;
pub type IHomePage = *mut ::core::ffi::c_void;
pub type IHomePageSetting = *mut ::core::ffi::c_void;
pub type IIEWebDriverManager = *mut ::core::ffi::c_void;
pub type IIEWebDriverSite = *mut ::core::ffi::c_void;
pub type IImageDecodeEventSink = *mut ::core::ffi::c_void;
pub type IImageDecodeEventSink2 = *mut ::core::ffi::c_void;
pub type IImageDecodeFilter = *mut ::core::ffi::c_void;
pub type IIntelliForms = *mut ::core::ffi::c_void;
pub type IInternetExplorerManager = *mut ::core::ffi::c_void;
pub type IInternetExplorerManager2 = *mut ::core::ffi::c_void;
pub type ILayoutRect = *mut ::core::ffi::c_void;
pub type IMapMIMEToCLSID = *mut ::core::ffi::c_void;
pub type IMediaActivityNotifySite = *mut ::core::ffi::c_void;
pub type IOpenService = *mut ::core::ffi::c_void;
pub type IOpenServiceActivity = *mut ::core::ffi::c_void;
pub type IOpenServiceActivityCategory = *mut ::core::ffi::c_void;
pub type IOpenServiceActivityInput = *mut ::core::ffi::c_void;
pub type IOpenServiceActivityManager = *mut ::core::ffi::c_void;
pub type IOpenServiceActivityOutputContext = *mut ::core::ffi::c_void;
pub type IOpenServiceManager = *mut ::core::ffi::c_void;
pub type IPeerFactory = *mut ::core::ffi::c_void;
pub type IPersistHistory = *mut ::core::ffi::c_void;
pub type IPrintTaskRequestFactory = *mut ::core::ffi::c_void;
pub type IPrintTaskRequestHandler = *mut ::core::ffi::c_void;
pub type IScrollableContextMenu = *mut ::core::ffi::c_void;
pub type IScrollableContextMenu2 = *mut ::core::ffi::c_void;
pub type ISniffStream = *mut ::core::ffi::c_void;
pub type ISurfacePresenterFlip = *mut ::core::ffi::c_void;
pub type ISurfacePresenterFlip2 = *mut ::core::ffi::c_void;
pub type ISurfacePresenterFlipBuffer = *mut ::core::ffi::c_void;
pub type ITargetContainer = *mut ::core::ffi::c_void;
pub type ITargetEmbedding = *mut ::core::ffi::c_void;
pub type ITargetFrame = *mut ::core::ffi::c_void;
pub type ITargetFrame2 = *mut ::core::ffi::c_void;
pub type ITargetFramePriv = *mut ::core::ffi::c_void;
pub type ITargetFramePriv2 = *mut ::core::ffi::c_void;
pub type ITargetNotify = *mut ::core::ffi::c_void;
pub type ITargetNotify2 = *mut ::core::ffi::c_void;
pub type ITimer = *mut ::core::ffi::c_void;
pub type ITimerEx = *mut ::core::ffi::c_void;
pub type ITimerService = *mut ::core::ffi::c_void;
pub type ITimerSink = *mut ::core::ffi::c_void;
pub type ITridentTouchInput = *mut ::core::ffi::c_void;
pub type ITridentTouchInputSite = *mut ::core::ffi::c_void;
pub type IUrlHistoryNotify = *mut ::core::ffi::c_void;
pub type IUrlHistoryStg = *mut ::core::ffi::c_void;
pub type IUrlHistoryStg2 = *mut ::core::ffi::c_void;
pub type IViewObjectPresentFlip = *mut ::core::ffi::c_void;
pub type IViewObjectPresentFlip2 = *mut ::core::ffi::c_void;
pub type IViewObjectPresentFlipSite = *mut ::core::ffi::c_void;
pub type IViewObjectPresentFlipSite2 = *mut ::core::ffi::c_void;
pub type IWebBrowserEventsService = *mut ::core::ffi::c_void;
pub type IWebBrowserEventsUrlService = *mut ::core::ffi::c_void;
pub type Iwfolders = *mut ::core::ffi::c_void;
pub const ADDRESSBAND: u32 = 2u32;
pub const ADDURL_ADDTOCACHE: ADDURL_FLAG = 1i32;
pub const ADDURL_ADDTOHISTORYANDCACHE: ADDURL_FLAG = 0i32;
pub const ADDURL_FIRST: ADDURL_FLAG = 0i32;
pub const ADDURL_Max: ADDURL_FLAG = 2147483647i32;
pub const ActivityContentCount: OpenServiceActivityContentType = 3i32;
pub const ActivityContentDocument: OpenServiceActivityContentType = 0i32;
pub const ActivityContentLink: OpenServiceActivityContentType = 2i32;
pub const ActivityContentNone: OpenServiceActivityContentType = -1i32;
pub const ActivityContentSelection: OpenServiceActivityContentType = 1i32;
pub const AnchorClick: ::windows_sys::core::GUID = ::windows_sys::core::GUID::from_u128(0x13d5413c_33b9_11d2_95a7_00c04f8ecb02);
pub const CATID_MSOfficeAntiVirus: ::windows_sys::core::GUID = ::windows_sys::core::GUID::from_u128(0x56ffcc30_d398_11d0_b2ae_00a0c908fa49);
pub const CDeviceRect: ::windows_sys::core::GUID = ::windows_sys::core::GUID::from_u128(0x3050f6d4_98b5_11cf_bb82_00aa00bdce0b);
pub const CDownloadBehavior: ::windows_sys::core::GUID = ::windows_sys::core::GUID::from_u128(0x3050f5be_98b5_11cf_bb82_00aa00bdce0b);
pub const CHeaderFooter: ::windows_sys::core::GUID = ::windows_sys::core::GUID::from_u128(0x3050f6cd_98b5_11cf_bb82_00aa00bdce0b);
pub const CLayoutRect: ::windows_sys::core::GUID = ::windows_sys::core::GUID::from_u128(0x3050f664_98b5_11cf_bb82_00aa00bdce0b);
pub const COLOR_NO_TRANSPARENT: u32 = 4294967295u32;
pub const CPersistDataPeer: ::windows_sys::core::GUID = ::windows_sys::core::GUID::from_u128(0x3050f487_98b5_11cf_bb82_00aa00bdce0b);
pub const CPersistHistory: ::windows_sys::core::GUID = ::windows_sys::core::GUID::from_u128(0x3050f4c8_98b5_11cf_bb82_00aa00bdce0b);
pub const CPersistShortcut: ::windows_sys::core::GUID = ::windows_sys::core::GUID::from_u128(0x3050f4c6_98b5_11cf_bb82_00aa00bdce0b);
pub const CPersistSnapshot: ::windows_sys::core::GUID = ::windows_sys::core::GUID::from_u128(0x3050f4c9_98b5_11cf_bb82_00aa00bdce0b);
pub const CPersistUserData: ::windows_sys::core::GUID = ::windows_sys::core::GUID::from_u128(0x3050f48e_98b5_11cf_bb82_00aa00bdce0b);
pub const CoDitherToRGB8: ::windows_sys::core::GUID = ::windows_sys::core::GUID::from_u128(0xa860ce50_3910_11d0_86fc_00a0c913f750);
pub const CoMapMIMEToCLSID: ::windows_sys::core::GUID = ::windows_sys::core::GUID::from_u128(0x30c3b080_30fb_11d0_b724_00aa006c1a01);
pub const CoSniffStream: ::windows_sys::core::GUID = ::windows_sys::core::GUID::from_u128(0x6a01fda0_30df_11d0_b724_00aa006c1a01);
pub const DISPID_ACTIVEXFILTERINGENABLED: u32 = 61u32;
pub const DISPID_ADDCHANNEL: u32 = 5u32;
pub const DISPID_ADDDESKTOPCOMPONENT: u32 = 6u32;
pub const DISPID_ADDFAVORITE: u32 = 4u32;
pub const DISPID_ADDSEARCHPROVIDER: u32 = 14u32;
pub const DISPID_ADDSERVICE: u32 = 30u32;
pub const DISPID_ADDSITEMODE: u32 = 49u32;
pub const DISPID_ADDTHUMBNAILBUTTONS: u32 = 48u32;
pub const DISPID_ADDTOFAVORITESBAR: u32 = 32u32;
pub const DISPID_ADDTRACKINGPROTECTIONLIST: u32 = 57u32;
pub const DISPID_ADVANCEERROR: u32 = 10u32;
pub const DISPID_AMBIENT_OFFLINEIFNOTCONNECTED: i32 = -5501i32;
pub const DISPID_AMBIENT_SILENT: i32 = -5502i32;
pub const DISPID_AUTOCOMPLETEATTACH: u32 = 12u32;
pub const DISPID_AUTOCOMPLETESAVEFORM: u32 = 10u32;
pub const DISPID_AUTOSCAN: u32 = 11u32;
pub const DISPID_BEFORENAVIGATE: u32 = 100u32;
pub const DISPID_BEFORENAVIGATE2: u32 = 250u32;
pub const DISPID_BEFORESCRIPTEXECUTE: u32 = 290u32;
pub const DISPID_BRANDIMAGEURI: u32 = 20u32;
pub const DISPID_BUILDNEWTABPAGE: u32 = 33u32;
pub const DISPID_CANADVANCEERROR: u32 = 12u32;
pub const DISPID_CANRETREATERROR: u32 = 13u32;
pub const DISPID_CHANGEDEFAULTBROWSER: u32 = 68u32;
pub const DISPID_CLEARNOTIFICATION: u32 = 71u32;
pub const DISPID_CLEARSITEMODEICONOVERLAY: u32 = 45u32;
pub const DISPID_CLIENTTOHOSTWINDOW: u32 = 268u32;
pub const DISPID_COMMANDSTATECHANGE: u32 = 105u32;
pub const DISPID_CONTENTDISCOVERYRESET: u32 = 36u32;
pub const DISPID_COUNTVIEWTYPES: u32 = 22u32;
pub const DISPID_CREATESUBSCRIPTION: u32 = 11u32;
pub const DISPID_CUSTOMIZECLEARTYPE: u32 = 23u32;
pub const DISPID_CUSTOMIZESETTINGS: u32 = 17u32;
pub const DISPID_DEFAULTSEARCHPROVIDER: u32 = 26u32;
pub const DISPID_DELETESUBSCRIPTION: u32 = 12u32;
pub const DISPID_DEPTH: u32 = 17u32;
pub const DISPID_DIAGNOSECONNECTION: u32 = 22u32;
pub const DISPID_DIAGNOSECONNECTIONUILESS: u32 = 66u32;
pub const DISPID_DOCUMENTCOMPLETE: u32 = 259u32;
pub const DISPID_DOUBLECLICK: u32 = 3u32;
pub const DISPID_DOWNLOADBEGIN: u32 = 106u32;
pub const DISPID_DOWNLOADCOMPLETE: u32 = 104u32;
pub const DISPID_ENABLENOTIFICATIONQUEUE: u32 = 72u32;
pub const DISPID_ENABLENOTIFICATIONQUEUELARGE: u32 = 78u32;
pub const DISPID_ENABLENOTIFICATIONQUEUESQUARE: u32 = 76u32;
pub const DISPID_ENABLENOTIFICATIONQUEUEWIDE: u32 = 77u32;
pub const DISPID_ENABLESUGGESTEDSITES: u32 = 39u32;
pub const DISPID_ENUMOPTIONS: u32 = 14u32;
pub const DISPID_EXPAND: u32 = 25u32;
pub const DISPID_EXPORT: u32 = 7u32;
pub const DISPID_FAVSELECTIONCHANGE: u32 = 1u32;
pub const DISPID_FILEDOWNLOAD: u32 = 270u32;
pub const DISPID_FLAGS: u32 = 19u32;
pub const DISPID_FRAMEBEFORENAVIGATE: u32 = 200u32;
pub const DISPID_FRAMENAVIGATECOMPLETE: u32 = 201u32;
pub const DISPID_FRAMENEWWINDOW: u32 = 204u32;
pub const DISPID_GETALWAYSSHOWLOCKSTATE: u32 = 23u32;
pub const DISPID_GETCVLISTDATA: u32 = 93u32;
pub const DISPID_GETCVLISTLOCALDATA: u32 = 94u32;
pub const DISPID_GETDETAILSSTATE: u32 = 19u32;
pub const DISPID_GETEMIELISTDATA: u32 = 95u32;
pub const DISPID_GETEMIELISTLOCALDATA: u32 = 96u32;
pub const DISPID_GETERRORCHAR: u32 = 15u32;
pub const DISPID_GETERRORCODE: u32 = 16u32;
pub const DISPID_GETERRORLINE: u32 = 14u32;
pub const DISPID_GETERRORMSG: u32 = 17u32;
pub const DISPID_GETERRORURL: u32 = 18u32;
pub const DISPID_GETEXPERIMENTALFLAG: u32 = 85u32;
pub const DISPID_GETEXPERIMENTALVALUE: u32 = 87u32;
pub const DISPID_GETNEEDHVSIAUTOLAUNCHFLAG: u32 = 100u32;
pub const DISPID_GETNEEDIEAUTOLAUNCHFLAG: u32 = 89u32;
pub const DISPID_GETOSSKU: u32 = 103u32;
pub const DISPID_GETPERERRSTATE: u32 = 21u32;
pub const DISPID_HASNEEDHVSIAUTOLAUNCHFLAG: u32 = 102u32;
pub const DISPID_HASNEEDIEAUTOLAUNCHFLAG: u32 = 88u32;
pub const DISPID_IMPORT: u32 = 6u32;
pub const DISPID_IMPORTEXPORTFAVORITES: u32 = 9u32;
pub const DISPID_INITIALIZED: u32 = 4u32;
pub const DISPID_INPRIVATEFILTERINGENABLED: u32 = 37u32;
pub const DISPID_INVOKECONTEXTMENU: u32 = 8u32;
pub const DISPID_ISMETAREFERRERAVAILABLE: u32 = 83u32;
pub const DISPID_ISSEARCHMIGRATED: u32 = 25u32;
pub const DISPID_ISSEARCHPROVIDERINSTALLED: u32 = 24u32;
pub const DISPID_ISSERVICEINSTALLED: u32 = 31u32;
pub const DISPID_ISSITEMODE: u32 = 43u32;
pub const DISPID_ISSITEMODEFIRSTRUN: u32 = 59u32;
pub const DISPID_ISSUBSCRIBED: u32 = 7u32;
pub const DISPID_LAUNCHIE: u32 = 91u32;
pub const DISPID_LAUNCHINHVSI: u32 = 99u32;
pub const DISPID_LAUNCHINTERNETOPTIONS: u32 = 74u32;
pub const DISPID_LAUNCHNETWORKCLIENTHELP: u32 = 67u32;
pub const DISPID_MODE: u32 = 18u32;
pub const DISPID_MOVESELECTIONDOWN: u32 = 2u32;
pub const DISPID_MOVESELECTIONTO: u32 = 9u32;
pub const DISPID_MOVESELECTIONUP: u32 = 1u32;
pub const DISPID_NAVIGATEANDFIND: u32 = 8u32;
pub const DISPID_NAVIGATECOMPLETE: u32 = 101u32;
pub const DISPID_NAVIGATECOMPLETE2: u32 = 252u32;
pub const DISPID_NAVIGATEERROR: u32 = 271u32;
pub const DISPID_NAVIGATETOSUGGESTEDSITES: u32 = 40u32;
pub const DISPID_NEWFOLDER: u32 = 4u32;
pub const DISPID_NEWPROCESS: u32 = 284u32;
pub const DISPID_NEWWINDOW: u32 = 107u32;
pub const DISPID_NEWWINDOW2: u32 = 251u32;
pub const DISPID_NEWWINDOW3: u32 = 273u32;
pub const DISPID_NSCOLUMNS: u32 = 21u32;
pub const DISPID_ONADDRESSBAR: u32 = 261u32;
pub const DISPID_ONFULLSCREEN: u32 = 258u32;
pub const DISPID_ONMENUBAR: u32 = 256u32;
pub const DISPID_ONQUIT: u32 = 253u32;
pub const DISPID_ONSTATUSBAR: u32 = 257u32;
pub const DISPID_ONTHEATERMODE: u32 = 260u32;
pub const DISPID_ONTOOLBAR: u32 = 255u32;
pub const DISPID_ONVISIBLE: u32 = 254u32;
pub const DISPID_OPENFAVORITESPANE: u32 = 97u32;
pub const DISPID_OPENFAVORITESSETTINGS: u32 = 98u32;
pub const DISPID_PHISHINGENABLED: u32 = 19u32;
pub const DISPID_PINNEDSITESTATE: u32 = 73u32;
pub const DISPID_PRINTTEMPLATEINSTANTIATION: u32 = 225u32;
pub const DISPID_PRINTTEMPLATETEARDOWN: u32 = 226u32;
pub const DISPID_PRIVACYIMPACTEDSTATECHANGE: u32 = 272u32;
pub const DISPID_PROGRESSCHANGE: u32 = 108u32;
pub const DISPID_PROPERTYCHANGE: u32 = 112u32;
pub const DISPID_PROVISIONNETWORKS: u32 = 62u32;
pub const DISPID_QUIT: u32 = 103u32;
pub const DISPID_REDIRECTXDOMAINBLOCKED: u32 = 286u32;
pub const DISPID_REFRESHOFFLINEDESKTOP: u32 = 3u32;
pub const DISPID_REMOVESCHEDULEDTILENOTIFICATION: u32 = 80u32;
pub const DISPID_REPORTSAFEURL: u32 = 63u32;
pub const DISPID_RESETEXPERIMENTALFLAGS: u32 = 92u32;
pub const DISPID_RESETFIRSTBOOTMODE: u32 = 1u32;
pub const DISPID_RESETSAFEMODE: u32 = 2u32;
pub const DISPID_RESETSORT: u32 = 3u32;
pub const DISPID_RETREATERROR: u32 = 11u32;
pub const DISPID_ROOT: u32 = 16u32;
pub const DISPID_RUNONCEHASSHOWN: u32 = 28u32;
pub const DISPID_RUNONCEREQUIREDSETTINGSCOMPLETE: u32 = 27u32;
pub const DISPID_RUNONCESHOWN: u32 = 15u32;
pub const DISPID_SCHEDULEDTILENOTIFICATION: u32 = 79u32;
pub const DISPID_SEARCHGUIDEURL: u32 = 29u32;
pub const DISPID_SELECTEDITEM: u32 = 15u32;
pub const DISPID_SELECTEDITEMS: u32 = 24u32;
pub const DISPID_SELECTIONCHANGE: u32 = 2u32;
pub const DISPID_SETACTIVITIESVISIBLE: u32 = 35u32;
pub const DISPID_SETDETAILSSTATE: u32 = 20u32;
pub const DISPID_SETEXPERIMENTALFLAG: u32 = 84u32;
pub const DISPID_SETEXPERIMENTALVALUE: u32 = 86u32;
pub const DISPID_SETMSDEFAULTS: u32 = 104u32;
pub const DISPID_SETNEEDHVSIAUTOLAUNCHFLAG: u32 = 101u32;
pub const DISPID_SETNEEDIEAUTOLAUNCHFLAG: u32 = 90u32;
pub const DISPID_SETPERERRSTATE: u32 = 22u32;
pub const DISPID_SETPHISHINGFILTERSTATUS: u32 = 282u32;
pub const DISPID_SETRECENTLYCLOSEDVISIBLE: u32 = 34u32;
pub const DISPID_SETROOT: u32 = 13u32;
pub const DISPID_SETSECURELOCKICON: u32 = 269u32;
pub const DISPID_SETSITEMODEICONOVERLAY: u32 = 44u32;
pub const DISPID_SETSITEMODEPROPERTIES: u32 = 50u32;
pub const DISPID_SETTHUMBNAILBUTTONS: u32 = 47u32;
pub const DISPID_SETVIEWTYPE: u32 = 23u32;
pub const DISPID_SHELLUIHELPERLAST: u32 = 105u32;
pub const DISPID_SHOWBROWSERUI: u32 = 13u32;
pub const DISPID_SHOWINPRIVATEHELP: u32 = 42u32;
pub const DISPID_SHOWTABSHELP: u32 = 41u32;
pub const DISPID_SITEMODEACTIVATE: u32 = 58u32;
pub const DISPID_SITEMODEADDBUTTONSTYLE: u32 = 54u32;
pub const DISPID_SITEMODEADDJUMPLISTITEM: u32 = 52u32;
pub const DISPID_SITEMODECLEARBADGE: u32 = 65u32;
pub const DISPID_SITEMODECLEARJUMPLIST: u32 = 53u32;
pub const DISPID_SITEMODECREATEJUMPLIST: u32 = 51u32;
pub const DISPID_SITEMODEREFRESHBADGE: u32 = 64u32;
pub const DISPID_SITEMODESHOWBUTTONSTYLE: u32 = 55u32;
pub const DISPID_SITEMODESHOWJUMPLIST: u32 = 56u32;
pub const DISPID_SKIPRUNONCE: u32 = 16u32;
pub const DISPID_SKIPTABSWELCOME: u32 = 21u32;
pub const DISPID_SQMENABLED: u32 = 18u32;
pub const DISPID_STARTBADGEUPDATE: u32 = 81u32;
pub const DISPID_STARTPERIODICUPDATE: u32 = 70u32;
pub const DISPID_STARTPERIODICUPDATEBATCH: u32 = 75u32;
pub const DISPID_STATUSTEXTCHANGE: u32 = 102u32;
pub const DISPID_STOPBADGEUPDATE: u32 = 82u32;
pub const DISPID_STOPPERIODICUPDATE: u32 = 69u32;
pub const DISPID_SUBSCRIPTIONSENABLED: u32 = 10u32;
pub const DISPID_SUGGESTEDSITESENABLED: u32 = 38u32;
pub const DISPID_SYNCHRONIZE: u32 = 5u32;
pub const DISPID_THIRDPARTYURLBLOCKED: u32 = 285u32;
pub const DISPID_TITLECHANGE: u32 = 113u32;
pub const DISPID_TITLEICONCHANGE: u32 = 114u32;
pub const DISPID_TRACKINGPROTECTIONENABLED: u32 = 60u32;
pub const DISPID_TVFLAGS: u32 = 20u32;
pub const DISPID_UNSELECTALL: u32 = 26u32;
pub const DISPID_UPDATEPAGESTATUS: u32 = 227u32;
pub const DISPID_UPDATETHUMBNAILBUTTON: u32 = 46u32;
pub const DISPID_VIEWUPDATE: u32 = 281u32;
pub const DISPID_WEBWORKERFINISHED: u32 = 289u32;
pub const DISPID_WEBWORKERSTARTED: u32 = 288u32;
pub const DISPID_WINDOWACTIVATE: u32 = 111u32;
pub const DISPID_WINDOWCLOSING: u32 = 263u32;
pub const DISPID_WINDOWMOVE: u32 = 109u32;
pub const DISPID_WINDOWREGISTERED: u32 = 200u32;
pub const DISPID_WINDOWRESIZE: u32 = 110u32;
pub const DISPID_WINDOWREVOKED: u32 = 201u32;
pub const DISPID_WINDOWSETHEIGHT: u32 = 267u32;
pub const DISPID_WINDOWSETLEFT: u32 = 264u32;
pub const DISPID_WINDOWSETRESIZABLE: u32 = 262u32;
pub const DISPID_WINDOWSETTOP: u32 = 265u32;
pub const DISPID_WINDOWSETWIDTH: u32 = 266u32;
pub const DISPID_WINDOWSTATECHANGED: u32 = 283u32;
pub const E_SURFACE_DISCARDED: i32 = -2147434493i32;
pub const E_SURFACE_NODC: i32 = -2147434492i32;
pub const E_SURFACE_NOSURFACE: i32 = -2147434496i32;
pub const E_SURFACE_NOTMYDC: i32 = -2147434491i32;
pub const E_SURFACE_NOTMYPOINTER: i32 = -2147434494i32;
pub const E_SURFACE_UNKNOWN_FORMAT: i32 = -2147434495i32;
pub const ExtensionValidationContextDynamic: ExtensionValidationContexts = 1i32;
pub const ExtensionValidationContextNone: ExtensionValidationContexts = 0i32;
pub const ExtensionValidationContextParsed: ExtensionValidationContexts = 2i32;
pub const ExtensionValidationResultArrestPageLoad: ExtensionValidationResults = 2i32;
pub const ExtensionValidationResultDoNotInstantiate: ExtensionValidationResults = 1i32;
pub const ExtensionValidationResultNone: ExtensionValidationResults = 0i32;
pub const FINDFRAME_INTERNAL: FINDFRAME_FLAGS = -2147483648i32;
pub const FINDFRAME_JUSTTESTEXISTENCE: FINDFRAME_FLAGS = 1i32;
pub const FINDFRAME_NONE: FINDFRAME_FLAGS = 0i32;
pub const FRAMEOPTIONS_BROWSERBAND: FRAMEOPTIONS_FLAGS = 64i32;
pub const FRAMEOPTIONS_DESKTOP: FRAMEOPTIONS_FLAGS = 32i32;
pub const FRAMEOPTIONS_NO3DBORDER: FRAMEOPTIONS_FLAGS = 16i32;
pub const FRAMEOPTIONS_NORESIZE: FRAMEOPTIONS_FLAGS = 8i32;
pub const FRAMEOPTIONS_SCROLL_AUTO: FRAMEOPTIONS_FLAGS = 4i32;
pub const FRAMEOPTIONS_SCROLL_NO: FRAMEOPTIONS_FLAGS = 2i32;
pub const FRAMEOPTIONS_SCROLL_YES: FRAMEOPTIONS_FLAGS = 1i32;
pub const HomePage: ::windows_sys::core::GUID = ::windows_sys::core::GUID::from_u128(0x766bf2ae_d650_11d1_9811_00c04fc31d2e);
pub const HomePageSetting: ::windows_sys::core::GUID = ::windows_sys::core::GUID::from_u128(0x374cede0_873a_4c4f_bc86_bcc8cf5116a3);
pub const IECMDID_ARG_CLEAR_FORMS_ALL: u32 = 0u32;
pub const IECMDID_ARG_CLEAR_FORMS_ALL_BUT_PASSWORDS: u32 = 1u32;
pub const IECMDID_ARG_CLEAR_FORMS_PASSWORDS_ONLY: u32 = 2u32;
pub const IECMDID_BEFORENAVIGATE_DOEXTERNALBROWSE: u32 = 3u32;
pub const IECMDID_BEFORENAVIGATE_GETIDLIST: u32 = 4u32;
pub const IECMDID_BEFORENAVIGATE_GETSHELLBROWSE: u32 = 2u32;
pub const IECMDID_CLEAR_AUTOCOMPLETE_FOR_FORMS: u32 = 0u32;
pub const IECMDID_GET_INVOKE_DEFAULT_BROWSER_ON_NEW_WINDOW: u32 = 6u32;
pub const IECMDID_SETID_AUTOCOMPLETE_FOR_FORMS: u32 = 1u32;
pub const IECMDID_SET_INVOKE_DEFAULT_BROWSER_ON_NEW_WINDOW: u32 = 5u32;
pub const IEGetProcessModule_PROC_NAME: ::windows_sys::core::PCSTR = ::windows_sys::core::s!("IEGetProcessModule");
pub const IEGetTabWindowExports_PROC_NAME: ::windows_sys::core::PCSTR = ::windows_sys::core::s!("IEGetTabWindowExports");
pub const IELAUNCHOPTION_FORCE_COMPAT: IELAUNCHOPTION_FLAGS = 2i32;
pub const IELAUNCHOPTION_FORCE_EDGE: IELAUNCHOPTION_FLAGS = 4i32;
pub const IELAUNCHOPTION_LOCK_ENGINE: IELAUNCHOPTION_FLAGS = 8i32;
pub const IELAUNCHOPTION_SCRIPTDEBUG: IELAUNCHOPTION_FLAGS = 1i32;
pub const IEPROCESS_MODULE_NAME: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("IERtUtil.dll");
pub const IEWebDriverManager: ::windows_sys::core::GUID = ::windows_sys::core::GUID::from_u128(0x90314af2_5250_47b3_89d8_6295fc23bc22);
pub const IE_USE_OE_MAIL_HKEY: i32 = -2147483647i32;
pub const IE_USE_OE_MAIL_KEY: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("Software\\Microsoft\\Internet Explorer\\Mail");
pub const IE_USE_OE_MAIL_VALUE: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("Use Outlook Express");
pub const IE_USE_OE_NEWS_HKEY: i32 = -2147483647i32;
pub const IE_USE_OE_NEWS_KEY: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("Software\\Microsoft\\Internet Explorer\\News");
pub const IE_USE_OE_NEWS_VALUE: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("Use Outlook Express");
pub const IE_USE_OE_PRESENT_HKEY: i32 = -2147483646i32;
pub const IE_USE_OE_PRESENT_KEY: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("Software\\Microsoft\\Windows\\CurrentVersion\\app.paths\\msimn.exe");
pub const IMGDECODE_EVENT_BEGINBITS: u32 = 4u32;
pub const IMGDECODE_EVENT_BITSCOMPLETE: u32 = 8u32;
pub const IMGDECODE_EVENT_PALETTE: u32 = 2u32;
pub const IMGDECODE_EVENT_PROGRESS: u32 = 1u32;
pub const IMGDECODE_EVENT_USEDDRAW: u32 = 16u32;
pub const IMGDECODE_HINT_BOTTOMUP: u32 = 2u32;
pub const IMGDECODE_HINT_FULLWIDTH: u32 = 4u32;
pub const IMGDECODE_HINT_TOPDOWN: u32 = 1u32;
pub const INTERNETEXPLORERCONFIGURATION_HOST: INTERNETEXPLORERCONFIGURATION = 1i32;
pub const INTERNETEXPLORERCONFIGURATION_WEB_DRIVER: INTERNETEXPLORERCONFIGURATION = 2i32;
pub const INTERNETEXPLORERCONFIGURATION_WEB_DRIVER_EDGE: INTERNETEXPLORERCONFIGURATION = 4i32;
pub const IntelliForms: ::windows_sys::core::GUID = ::windows_sys::core::GUID::from_u128(0x613ab92e_16bf_11d2_bca5_00c04fd929db);
pub const InternetExplorerManager: ::windows_sys::core::GUID = ::windows_sys::core::GUID::from_u128(0xdf4fcc34_067a_4e0a_8352_4a1a5095346e);
pub const LINKSBAND: u32 = 4u32;
pub const MAPMIME_CLSID: u32 = 1u32;
pub const MAPMIME_DEFAULT: u32 = 0u32;
pub const MAPMIME_DEFAULT_ALWAYS: u32 = 3u32;
pub const MAPMIME_DISABLE: u32 = 2u32;
pub const MAX_SEARCH_FORMAT_STRING: u32 = 255u32;
pub const MediaCasting: MEDIA_ACTIVITY_NOTIFY_TYPE = 2i32;
pub const MediaPlayback: MEDIA_ACTIVITY_NOTIFY_TYPE = 0i32;
pub const MediaRecording: MEDIA_ACTIVITY_NOTIFY_TYPE = 1i32;
pub const NAVIGATEFRAME_FL_AUTH_FAIL_CACHE_OK: NAVIGATEFRAME_FLAGS = 16i32;
pub const NAVIGATEFRAME_FL_NO_DOC_CACHE: NAVIGATEFRAME_FLAGS = 4i32;
pub const NAVIGATEFRAME_FL_NO_IMAGE_CACHE: NAVIGATEFRAME_FLAGS = 8i32;
pub const NAVIGATEFRAME_FL_POST: NAVIGATEFRAME_FLAGS = 2i32;
pub const NAVIGATEFRAME_FL_REALLY_SENDING_FROM_FORM: NAVIGATEFRAME_FLAGS = 64i32;
pub const NAVIGATEFRAME_FL_RECORD: NAVIGATEFRAME_FLAGS = 1i32;
pub const NAVIGATEFRAME_FL_SENDING_FROM_FORM: NAVIGATEFRAME_FLAGS = 32i32;
pub const OS_E_CANCELLED: OpenServiceErrors = -2147471631i32;
pub const OS_E_GPDISABLED: OpenServiceErrors = -1072886820i32;
pub const OS_E_NOTFOUND: OpenServiceErrors = -2147287038i32;
pub const OS_E_NOTSUPPORTED: OpenServiceErrors = -2147467231i32;
pub const OpenServiceActivityManager: ::windows_sys::core::GUID = ::windows_sys::core::GUID::from_u128(0xc5efd803_50f8_43cd_9ab8_aafc1394c9e0);
pub const OpenServiceManager: ::windows_sys::core::GUID = ::windows_sys::core::GUID::from_u128(0x098870b6_39ea_480b_b8b5_dd0167c4db59);
pub const PeerFactory: ::windows_sys::core::GUID = ::windows_sys::core::GUID::from_u128(0x3050f4cf_98b5_11cf_bb82_00aa00bdce0b);
pub const REGSTRA_VAL_STARTPAGE: ::windows_sys::core::PCSTR = ::windows_sys::core::s!("Start Page");
pub const REGSTR_PATH_CURRENT: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("current");
pub const REGSTR_PATH_DEFAULT: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("default");
pub const REGSTR_PATH_INETCPL_RESTRICTIONS: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("Software\\Policies\\Microsoft\\Internet Explorer\\Control Panel");
pub const REGSTR_PATH_MIME_DATABASE: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("MIME\\Database");
pub const REGSTR_PATH_REMOTEACCESS: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("RemoteAccess");
pub const REGSTR_PATH_REMOTEACESS: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("RemoteAccess");
pub const REGSTR_SHIFTQUICKSUFFIX: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("ShiftQuickCompleteSuffix");
pub const REGSTR_VAL_ACCEPT_LANGUAGE: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("AcceptLanguage");
pub const REGSTR_VAL_ACCESSMEDIUM: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("AccessMedium");
pub const REGSTR_VAL_ACCESSTYPE: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("AccessType");
pub const REGSTR_VAL_ALIASTO: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("AliasForCharset");
pub const REGSTR_VAL_ANCHORCOLOR: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("Anchor Color");
pub const REGSTR_VAL_ANCHORCOLORHOVER: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("Anchor Color Hover");
pub const REGSTR_VAL_ANCHORCOLORVISITED: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("Anchor Color Visited");
pub const REGSTR_VAL_ANCHORUNDERLINE: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("Anchor Underline");
pub const REGSTR_VAL_AUTODETECT: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("AutoDetect");
pub const REGSTR_VAL_AUTODIALDLLNAME: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("AutodialDllName");
pub const REGSTR_VAL_AUTODIALFCNNAME: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("AutodialFcnName");
pub const REGSTR_VAL_AUTODIAL_MONITORCLASSNAME: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("MS_AutodialMonitor");
pub const REGSTR_VAL_AUTODIAL_TRYONLYONCE: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("TryAutodialOnce");
pub const REGSTR_VAL_AUTONAVIGATE: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("SearchForExtensions");
pub const REGSTR_VAL_AUTOSEARCH: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("Do404Search");
pub const REGSTR_VAL_BACKBITMAP: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("BackBitmap");
pub const REGSTR_VAL_BACKGROUNDCOLOR: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("Background Color");
pub const REGSTR_VAL_BODYCHARSET: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("BodyCharset");
pub const REGSTR_VAL_BYPASSAUTOCONFIG: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("BypassAutoconfig");
pub const REGSTR_VAL_CACHEPREFIX: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("CachePrefix");
pub const REGSTR_VAL_CHECKASSOC: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("Check_Associations");
pub const REGSTR_VAL_CODEDOWNLOAD: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("Code Download");
pub const REGSTR_VAL_CODEDOWNLOAD_DEF: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("yes");
pub const REGSTR_VAL_CODEPAGE: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("CodePage");
pub const REGSTR_VAL_COVEREXCLUDE: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("CoverExclude");
pub const REGSTR_VAL_DAYSTOKEEP: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("DaysToKeep");
pub const REGSTR_VAL_DEFAULT_CODEPAGE: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("Default_CodePage");
pub const REGSTR_VAL_DEFAULT_SCRIPT: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("Default_Script");
pub const REGSTR_VAL_DEF_ENCODING: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("Default_Encoding");
pub const REGSTR_VAL_DEF_INETENCODING: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("Default_InternetEncoding");
pub const REGSTR_VAL_DESCRIPTION: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("Description");
pub const REGSTR_VAL_DIRECTORY: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("Directory");
pub const REGSTR_VAL_DISCONNECTIDLETIME: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("DisconnectIdleTime");
pub const REGSTR_VAL_ENABLEAUTODIAL: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("EnableAutodial");
pub const REGSTR_VAL_ENABLEAUTODIALDISCONNECT: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("EnableAutodisconnect");
pub const REGSTR_VAL_ENABLEAUTODISCONNECT: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("EnableAutodisconnect");
pub const REGSTR_VAL_ENABLEEXITDISCONNECT: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("EnableExitDisconnect");
pub const REGSTR_VAL_ENABLESECURITYCHECK: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("EnableSecurityCheck");
pub const REGSTR_VAL_ENABLEUNATTENDED: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("EnableUnattended");
pub const REGSTR_VAL_ENCODENAME: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("EncodingName");
pub const REGSTR_VAL_FAMILY: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("Family");
pub const REGSTR_VAL_FIXEDWIDTHFONT: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("FixedWidthFont");
pub const REGSTR_VAL_FIXED_FONT: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("IEFixedFontName");
pub const REGSTR_VAL_FONT_SCRIPT: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("Script");
pub const REGSTR_VAL_FONT_SCRIPTS: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("Scripts");
pub const REGSTR_VAL_FONT_SCRIPT_NAME: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("Script");
pub const REGSTR_VAL_FONT_SIZE: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("IEFontSize");
pub const REGSTR_VAL_FONT_SIZE_DEF: u32 = 2u32;
pub const REGSTR_VAL_HEADERCHARSET: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("HeaderCharset");
pub const REGSTR_VAL_HTTP_ERRORS: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("Friendly http errors");
pub const REGSTR_VAL_IE_CUSTOMCOLORS: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("Custom Colors");
pub const REGSTR_VAL_INETCPL_ADVANCEDTAB: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("AdvancedTab");
pub const REGSTR_VAL_INETCPL_CONNECTIONSTAB: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("ConnectionsTab");
pub const REGSTR_VAL_INETCPL_CONTENTTAB: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("ContentTab");
pub const REGSTR_VAL_INETCPL_GENERALTAB: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("GeneralTab");
pub const REGSTR_VAL_INETCPL_IEAK: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("IEAKContext");
pub const REGSTR_VAL_INETCPL_PRIVACYTAB: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("PrivacyTab");
pub const REGSTR_VAL_INETCPL_PROGRAMSTAB: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("ProgramsTab");
pub const REGSTR_VAL_INETCPL_SECURITYTAB: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("SecurityTab");
pub const REGSTR_VAL_INETENCODING: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("InternetEncoding");
pub const REGSTR_VAL_INTERNETENTRY: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("InternetProfile");
pub const REGSTR_VAL_INTERNETENTRYBKUP: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("BackupInternetProfile");
pub const REGSTR_VAL_INTERNETPROFILE: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("InternetProfile");
pub const REGSTR_VAL_JAVAJIT: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("EnableJIT");
pub const REGSTR_VAL_JAVAJIT_DEF: u32 = 0u32;
pub const REGSTR_VAL_JAVALOGGING: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("EnableLogging");
pub const REGSTR_VAL_JAVALOGGING_DEF: u32 = 0u32;
pub const REGSTR_VAL_LEVEL: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("Level");
pub const REGSTR_VAL_LOADIMAGES: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("Display Inline Images");
pub const REGSTR_VAL_LOCALPAGE: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("Local Page");
pub const REGSTR_VAL_MOSDISCONNECT: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("DisconnectTimeout");
pub const REGSTR_VAL_NEWDIRECTORY: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("NewDirectory");
pub const REGSTR_VAL_NONETAUTODIAL: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("NoNetAutodial");
pub const REGSTR_VAL_PLAYSOUNDS: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("Play_Background_Sounds");
pub const REGSTR_VAL_PLAYVIDEOS: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("Display Inline Videos");
pub const REGSTR_VAL_PRIVCONVERTER: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("PrivConverter");
pub const REGSTR_VAL_PROPORTIONALFONT: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("ProportionalFont");
pub const REGSTR_VAL_PROP_FONT: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("IEPropFontName");
pub const REGSTR_VAL_PROXYENABLE: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("ProxyEnable");
pub const REGSTR_VAL_PROXYOVERRIDE: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("ProxyOverride");
pub const REGSTR_VAL_PROXYSERVER: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("ProxyServer");
pub const REGSTR_VAL_REDIALATTEMPTS: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("RedialAttempts");
pub const REGSTR_VAL_REDIALINTERVAL: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("RedialWait");
pub const REGSTR_VAL_RNAINSTALLED: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("Installed");
pub const REGSTR_VAL_SAFETYWARNINGLEVEL: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("Safety Warning Level");
pub const REGSTR_VAL_SCHANNELENABLEPROTOCOL: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("Enabled");
pub const REGSTR_VAL_SCHANNELENABLEPROTOCOL_DEF: u32 = 1u32;
pub const REGSTR_VAL_SCRIPT_FIXED_FONT: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("IEFixedFontName");
pub const REGSTR_VAL_SCRIPT_PROP_FONT: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("IEPropFontName");
pub const REGSTR_VAL_SEARCHPAGE: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("Search Page");
pub const REGSTR_VAL_SECURITYACTICEXSCRIPTS: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("Security_RunScripts");
pub const REGSTR_VAL_SECURITYACTICEXSCRIPTS_DEF: u32 = 1u32;
pub const REGSTR_VAL_SECURITYACTIVEX: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("Security_RunActiveXControls");
pub const REGSTR_VAL_SECURITYACTIVEX_DEF: u32 = 1u32;
pub const REGSTR_VAL_SECURITYALLOWCOOKIES: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("AllowCookies");
pub const REGSTR_VAL_SECURITYALLOWCOOKIES_DEF: u32 = 1u32;
pub const REGSTR_VAL_SECURITYDISABLECACHINGOFSSLPAGES: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("DisableCachingOfSSLPages");
pub const REGSTR_VAL_SECURITYDISABLECACHINGOFSSLPAGES_DEF: u32 = 0u32;
pub const REGSTR_VAL_SECURITYJAVA: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("Security_RunJavaApplets");
pub const REGSTR_VAL_SECURITYJAVA_DEF: u32 = 1u32;
pub const REGSTR_VAL_SECURITYWARNONBADCERTSENDING: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("WarnOnBadCertSending");
pub const REGSTR_VAL_SECURITYWARNONBADCERTSENDING_DEF: u32 = 1u32;
pub const REGSTR_VAL_SECURITYWARNONBADCERTVIEWING: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("WarnOnBadCertRecving");
pub const REGSTR_VAL_SECURITYWARNONBADCERTVIEWING_DEF: u32 = 1u32;
pub const REGSTR_VAL_SECURITYWARNONSEND: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("WarnOnPost");
pub const REGSTR_VAL_SECURITYWARNONSENDALWAYS: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("WarnAlwaysOnPost");
pub const REGSTR_VAL_SECURITYWARNONSENDALWAYS_DEF: u32 = 1u32;
pub const REGSTR_VAL_SECURITYWARNONSEND_DEF: u32 = 1u32;
pub const REGSTR_VAL_SECURITYWARNONVIEW: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("WarnOnView");
pub const REGSTR_VAL_SECURITYWARNONVIEW_DEF: u32 = 1u32;
pub const REGSTR_VAL_SECURITYWARNONZONECROSSING: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("WarnOnZoneCrossing");
pub const REGSTR_VAL_SECURITYWARNONZONECROSSING_DEF: u32 = 1u32;
pub const REGSTR_VAL_SHOWADDRESSBAR: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("Show_URLToolBar");
pub const REGSTR_VAL_SHOWFOCUS: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("Tabstop - MouseDown");
pub const REGSTR_VAL_SHOWFOCUS_DEF: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("no");
pub const REGSTR_VAL_SHOWFULLURLS: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("Show_FullURL");
pub const REGSTR_VAL_SHOWTOOLBAR: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("Show_ToolBar");
pub const REGSTR_VAL_SMOOTHSCROLL: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("SmoothScroll");
pub const REGSTR_VAL_SMOOTHSCROLL_DEF: u32 = 1u32;
pub const REGSTR_VAL_STARTPAGE: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("Start Page");
pub const REGSTR_VAL_TEXTCOLOR: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("Text Color");
pub const REGSTR_VAL_TRUSTWARNINGLEVEL_HIGH: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("High");
pub const REGSTR_VAL_TRUSTWARNINGLEVEL_LOW: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("No Security");
pub const REGSTR_VAL_TRUSTWARNINGLEVEL_MED: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("Medium");
pub const REGSTR_VAL_USEAUTOAPPEND: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("Append Completion");
pub const REGSTR_VAL_USEAUTOCOMPLETE: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("Use AutoComplete");
pub const REGSTR_VAL_USEAUTOSUGGEST: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("AutoSuggest");
pub const REGSTR_VAL_USEDLGCOLORS: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("Use_DlgBox_Colors");
pub const REGSTR_VAL_USEHOVERCOLOR: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("Use Anchor Hover Color");
pub const REGSTR_VAL_USEIBAR: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("UseBar");
pub const REGSTR_VAL_USEICM: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("UseICM");
pub const REGSTR_VAL_USEICM_DEF: u32 = 0u32;
pub const REGSTR_VAL_USERAGENT: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("User Agent");
pub const REGSTR_VAL_USESTYLESHEETS: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("Use Stylesheets");
pub const REGSTR_VAL_USESTYLESHEETS_DEF: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("yes");
pub const REGSTR_VAL_VISIBLEBANDS: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("VisibleBands");
pub const REGSTR_VAL_VISIBLEBANDS_DEF: u32 = 7u32;
pub const REGSTR_VAL_WEBCHARSET: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("WebCharset");
pub const SCMP_BOTTOM: SCROLLABLECONTEXTMENU_PLACEMENT = 1i32;
pub const SCMP_FULL: SCROLLABLECONTEXTMENU_PLACEMENT = 4i32;
pub const SCMP_LEFT: SCROLLABLECONTEXTMENU_PLACEMENT = 2i32;
pub const SCMP_RIGHT: SCROLLABLECONTEXTMENU_PLACEMENT = 3i32;
pub const SCMP_TOP: SCROLLABLECONTEXTMENU_PLACEMENT = 0i32;
pub const STATURLFLAG_ISCACHED: u32 = 1u32;
pub const STATURLFLAG_ISTOPLEVEL: u32 = 2u32;
pub const STATURL_QUERYFLAG_ISCACHED: u32 = 65536u32;
pub const STATURL_QUERYFLAG_NOTITLE: u32 = 262144u32;
pub const STATURL_QUERYFLAG_NOURL: u32 = 131072u32;
pub const STATURL_QUERYFLAG_TOPLEVEL: u32 = 524288u32;
pub const SURFACE_LOCK_ALLOW_DISCARD: u32 = 2u32;
pub const SURFACE_LOCK_EXCLUSIVE: u32 = 1u32;
pub const SURFACE_LOCK_WAIT: u32 = 4u32;
pub const SZBACKBITMAP: ::windows_sys::core::PCSTR = ::windows_sys::core::s!("BackBitmap");
pub const SZJAVAVMPATH: ::windows_sys::core::PCSTR = ::windows_sys::core::s!("\\Java VM");
pub const SZNOTEXT: ::windows_sys::core::PCSTR = ::windows_sys::core::s!("NoText");
pub const SZTOOLBAR: ::windows_sys::core::PCSTR = ::windows_sys::core::s!("\\Toolbar");
pub const SZTRUSTWARNLEVEL: ::windows_sys::core::PCSTR = ::windows_sys::core::s!("Trust Warning Level");
pub const SZVISIBLE: ::windows_sys::core::PCSTR = ::windows_sys::core::s!("VisibleBands");
pub const SZ_IE_DEFAULT_HTML_EDITOR: ::windows_sys::core::PCSTR = ::windows_sys::core::s!("Default HTML Editor");
pub const SZ_IE_IBAR: ::windows_sys::core::PCSTR = ::windows_sys::core::s!("Bar");
pub const SZ_IE_IBAR_BANDS: ::windows_sys::core::PCSTR = ::windows_sys::core::s!("Bands");
pub const SZ_IE_MAIN: ::windows_sys::core::PCSTR = ::windows_sys::core::s!("Main");
pub const SZ_IE_SEARCHSTRINGS: ::windows_sys::core::PCSTR = ::windows_sys::core::s!("UrlTemplate");
pub const SZ_IE_SECURITY: ::windows_sys::core::PCSTR = ::windows_sys::core::s!("Security");
pub const SZ_IE_SETTINGS: ::windows_sys::core::PCSTR = ::windows_sys::core::s!("Settings");
pub const SZ_IE_THRESHOLDS: ::windows_sys::core::PCSTR = ::windows_sys::core::s!("ErrorThresholds");
pub const S_SURFACE_DISCARDED: i32 = 49155i32;
pub const TARGET_NOTIFY_OBJECT_NAME: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("863a99a0-21bc-11d0-82b4-00a0c90c29c5");
pub const TF_NAVIGATE: u32 = 2142153644u32;
pub const TIMERMODE_NORMAL: u32 = 0u32;
pub const TIMERMODE_VISIBILITYAWARE: u32 = 1u32;
pub const TOOLSBAND: u32 = 1u32;
pub const TSZCALENDARPROTOCOL: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("unk");
pub const TSZCALLTOPROTOCOL: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("callto");
pub const TSZINTERNETCLIENTSPATH: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("Software\\Microsoft\\Internet Explorer\\Unix");
pub const TSZLDAPPROTOCOL: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("ldap");
pub const TSZMAILTOPROTOCOL: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("mailto");
pub const TSZMICROSOFTPATH: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("Software\\Microsoft");
pub const TSZNEWSPROTOCOL: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("news");
pub const TSZPROTOCOLSPATH: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("Protocols\\");
pub const TSZSCHANNELPATH: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("SYSTEM\\CurrentControlSet\\Control\\SecurityProviders\\SCHANNEL");
pub const TSZVSOURCEPROTOCOL: ::windows_sys::core::PCWSTR = ::windows_sys::core::w!("view source");
pub const msodsvFailed: u32 = 3u32;
pub const msodsvLowSecurityLevel: u32 = 4u32;
pub const msodsvNoMacros: u32 = 0u32;
pub const msodsvPassedTrusted: u32 = 2u32;
pub const msodsvPassedTrustedCert: u32 = 5u32;
pub const msodsvUnsigned: u32 = 1u32;
pub const msoedmDisable: u32 = 2u32;
pub const msoedmDontOpen: u32 = 3u32;
pub const msoedmEnable: u32 = 1u32;
pub const msoslHigh: u32 = 3u32;
pub const msoslMedium: u32 = 2u32;
pub const msoslNone: u32 = 1u32;
pub const msoslUndefined: u32 = 0u32;
pub const wfolders: ::windows_sys::core::GUID = ::windows_sys::core::GUID::from_u128(0xbae31f9a_1b81_11d2_a97a_00c04f8ecb02);
pub type ADDURL_FLAG = i32;
pub type ExtensionValidationContexts = i32;
pub type ExtensionValidationResults = i32;
pub type FINDFRAME_FLAGS = i32;
pub type FRAMEOPTIONS_FLAGS = i32;
pub type IELAUNCHOPTION_FLAGS = i32;
pub type INTERNETEXPLORERCONFIGURATION = i32;
pub type MEDIA_ACTIVITY_NOTIFY_TYPE = i32;
pub type NAVIGATEFRAME_FLAGS = i32;
pub type OpenServiceActivityContentType = i32;
pub type OpenServiceErrors = i32;
pub type SCROLLABLECONTEXTMENU_PLACEMENT = i32;
#[repr(C)]
pub struct IELAUNCHURLINFO {
    pub cbSize: u32,
    pub dwCreationFlags: u32,
    pub dwLaunchOptionFlags: u32,
}
impl ::core::marker::Copy for IELAUNCHURLINFO {}
impl ::core::clone::Clone for IELAUNCHURLINFO {
    fn clone(&self) -> Self {
        *self
    }
}
#[repr(C)]
pub struct NAVIGATEDATA {
    pub ulTarget: u32,
    pub ulURL: u32,
    pub ulRefURL: u32,
    pub ulPostData: u32,
    pub dwFlags: u32,
}
impl ::core::marker::Copy for NAVIGATEDATA {}
impl ::core::clone::Clone for NAVIGATEDATA {
    fn clone(&self) -> Self {
        *self
    }
}
#[repr(C)]
#[doc = "Required features: `\"Win32_Foundation\"`"]
#[cfg(feature = "Win32_Foundation")]
pub struct STATURL {
    pub cbSize: u32,
    pub pwcsUrl: ::windows_sys::core::PWSTR,
    pub pwcsTitle: ::windows_sys::core::PWSTR,
    pub ftLastVisited: super::super::Foundation::FILETIME,
    pub ftLastUpdated: super::super::Foundation::FILETIME,
    pub ftExpires: super::super::Foundation::FILETIME,
    pub dwFlags: u32,
}
#[cfg(feature = "Win32_Foundation")]
impl ::core::marker::Copy for STATURL {}
#[cfg(feature = "Win32_Foundation")]
impl ::core::clone::Clone for STATURL {
    fn clone(&self) -> Self {
        *self
    }
}
