{"rustc": 3894273698020608664, "features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"full\", \"parsing\", \"printing\", \"proc-macro\", \"quote\", \"visit-mut\"]", "declared_features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"fold\", \"full\", \"parsing\", \"printing\", \"proc-macro\", \"quote\", \"test\", \"visit\", \"visit-mut\"]", "target": 17750462924906641010, "profile": 17984201634715228204, "path": 7666517967577705226, "deps": [[9548926982877553556, "quote", false, 13823154764320488058], [10418434610764581512, "unicode_ident", false, 18052843724359628508], [14617299571892049630, "proc_macro2", false, 2987192976512377720]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/syn-e8b19caf13b5cc6e/dep-lib-syn", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}