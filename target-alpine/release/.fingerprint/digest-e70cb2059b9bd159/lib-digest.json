{"rustc": 3894273698020608664, "features": "[\"alloc\", \"block-buffer\", \"const-oid\", \"core-api\", \"default\", \"mac\", \"oid\", \"std\", \"subtle\"]", "declared_features": "[\"alloc\", \"blobby\", \"block-buffer\", \"const-oid\", \"core-api\", \"default\", \"dev\", \"mac\", \"oid\", \"rand_core\", \"std\", \"subtle\"]", "target": 7510122432137863311, "profile": 17665183640080672258, "path": 11602851731793320706, "deps": [[2352660017780662552, "crypto_common", false, 14901985677319084408], [5343333008895563666, "subtle", false, 3292345243749672827], [8066688306558157009, "const_oid", false, 7945248312629491287], [10626340395483396037, "block_buffer", false, 403749958774031804]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/digest-e70cb2059b9bd159/dep-lib-digest", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}