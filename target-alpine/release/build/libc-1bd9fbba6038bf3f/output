cargo:rerun-if-changed=build.rs
cargo:rustc-cfg=freebsd11
cargo:rustc-cfg=libc_priv_mod_use
cargo:rustc-cfg=libc_union
cargo:rustc-cfg=libc_const_size_of
cargo:rustc-cfg=libc_align
cargo:rustc-cfg=libc_int128
cargo:rustc-cfg=libc_core_cvoid
cargo:rustc-cfg=libc_packedN
cargo:rustc-cfg=libc_cfg_target_vendor
cargo:rustc-cfg=libc_non_exhaustive
cargo:rustc-cfg=libc_long_array
cargo:rustc-cfg=libc_ptr_addr_of
cargo:rustc-cfg=libc_underscore_const_names
cargo:rustc-cfg=libc_const_extern_fn
cargo:rustc-check-cfg=cfg(emscripten_new_stat_abi)
cargo:rustc-check-cfg=cfg(espidf_time64)
cargo:rustc-check-cfg=cfg(freebsd10)
cargo:rustc-check-cfg=cfg(freebsd11)
cargo:rustc-check-cfg=cfg(freebsd12)
cargo:rustc-check-cfg=cfg(freebsd13)
cargo:rustc-check-cfg=cfg(freebsd14)
cargo:rustc-check-cfg=cfg(freebsd15)
cargo:rustc-check-cfg=cfg(libc_align)
cargo:rustc-check-cfg=cfg(libc_cfg_target_vendor)
cargo:rustc-check-cfg=cfg(libc_const_extern_fn)
cargo:rustc-check-cfg=cfg(libc_const_extern_fn_unstable)
cargo:rustc-check-cfg=cfg(libc_const_size_of)
cargo:rustc-check-cfg=cfg(libc_core_cvoid)
cargo:rustc-check-cfg=cfg(libc_deny_warnings)
cargo:rustc-check-cfg=cfg(libc_int128)
cargo:rustc-check-cfg=cfg(libc_long_array)
cargo:rustc-check-cfg=cfg(libc_non_exhaustive)
cargo:rustc-check-cfg=cfg(libc_packedN)
cargo:rustc-check-cfg=cfg(libc_priv_mod_use)
cargo:rustc-check-cfg=cfg(libc_ptr_addr_of)
cargo:rustc-check-cfg=cfg(libc_thread_local)
cargo:rustc-check-cfg=cfg(libc_underscore_const_names)
cargo:rustc-check-cfg=cfg(libc_union)
cargo:rustc-check-cfg=cfg(target_os,values("switch","aix","ohos","hurd","visionos"))
cargo:rustc-check-cfg=cfg(target_env,values("illumos","wasi","aix","ohos"))
cargo:rustc-check-cfg=cfg(target_arch,values("loongarch64","mips32r6","mips64r6","csky"))
