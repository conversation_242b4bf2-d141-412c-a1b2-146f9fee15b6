//! Example demonstrating TAMTIL's URL-based actor addressing
//! 
//! This example shows:
//! - Platform spawning context actors
//! - Context actors spawning regular actors  
//! - URL-based addressing: platform.com/context_name/context_id/actor_name/actor_id
//! - Inter-actor communication via actors.actor(id).act(action) pattern
//! - rkyv zero-copy serialization

use tamtil::example::*;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // Initialize tracing for better output
    tracing_subscriber::fmt::init();
    
    println!("🚀 TAMTIL URL Addressing Example");
    println!("================================");
    
    // Run the hierarchical spawning example
    run_hierarchical_spawning_example().await?;
    
    println!("\n📡 Inter-Actor Communication Example");
    println!("====================================");
    
    // Run the inter-actor communication example
    run_inter_actor_communication_example().await?;
    
    println!("\n🎉 All examples completed successfully!");
    println!("\n💡 Key takeaways:");
    println!("   - Actors use URL-based IDs: platform.com/context/actor");
    println!("   - Inter-actor communication: actors.actor(id).act(action)");
    println!("   - rkyv provides zero-copy serialization");
    println!("   - Platform spawns contexts, contexts spawn actors");
    println!("   - All actors follow action->reaction pattern");
    
    Ok(())
}
