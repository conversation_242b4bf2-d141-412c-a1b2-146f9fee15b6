//! Complete TAMTIL Example - Demonstrates all features
//!
//! This example showcases:
//! - Hierarchical actor system: Platform → Context → Actor → Child
//! - URL-based addressing: platform.com/context/context_id/actor/actor_id/child/child_id
//! - Inter-actor communication via actors.actor(id).act(action) pattern
//! - Child actor spawning via actors.spawn(name)
//! - Automatic child cleanup when parent stops
//! - rkyv zero-copy serialization
//! - Action→reaction pattern throughout
//! - Single-word naming convention

use tamtil::{platform, context, actor, ActorId, Platform, TamtilResult};

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // Initialize tracing for better output
    tracing_subscriber::fmt::init();

    println!("🚀 TAMTIL Complete Feature Demonstration");
    println!("========================================");

    // Run the complete example
    run_complete_example().await?;

    println!("\n🎉 All TAMTIL features demonstrated successfully!");
    println!("\n💡 Key features showcased:");
    println!("   ✅ Hierarchical addressing: platform.com/context/context_id/actor/actor_id/child/child_id");
    println!("   ✅ Actor spawning: actors.spawn(name)");
    println!("   ✅ Inter-actor communication: actors.actor(id).act(action)");
    println!("   ✅ Automatic child cleanup on parent stop");
    println!("   ✅ rkyv zero-copy serialization");
    println!("   ✅ Action→reaction pattern");
    println!("   ✅ Single-word naming convention");
    println!("   ✅ No locking - elegant concurrency");

    Ok(())
}

/// Complete example demonstrating all TAMTIL features
async fn run_complete_example() -> TamtilResult<()> {
    println!("\n📍 Demonstrating hierarchical addressing system:");
    println!("   platform.com");
    println!("   ├── web (context)");
    println!("   │   ├── main (context instance)");
    println!("   │   │   ├── counter (actor)");
    println!("   │   │   │   ├── stats (actor instance)");
    println!("   │   │   │   │   └── session (child actor)");
    println!("   │   │   │   │       └── user123 (child instance)");

    // 1. Create platform actor
    let platform_id = ActorId::new("platform.com");
    let platform_actor = platform::Actor::new(platform_id.clone());
    let platform = Platform::new();
    let platform_handle = platform.spawn(platform_id.clone(), platform_actor).await?;

    // Start platform
    let reaction = platform_handle.act(platform::Action::Standard(tamtil::StandardAction::Start)).await?;
    println!("\n✅ Platform started: {:?}", reaction);

    // 2. Create context actor
    let context_id = ActorId::new("platform.com/web");
    let context_actor = context::Actor::new(context_id.clone());
    let context_handle = platform.spawn(context_id.clone(), context_actor).await?;

    // Start context
    let reaction = context_handle.act(context::Action::Standard(tamtil::StandardAction::Start)).await?;
    println!("✅ Context started: {:?}", reaction);

    // 3. Create counter actor
    let counter_id = ActorId::new("platform.com/web/main");
    let counter_actor = actor::Counter::new(counter_id.clone());
    let counter_handle = platform.spawn(counter_id.clone(), counter_actor).await?;

    // Start counter
    let reaction = counter_handle.act(actor::CounterAction::Start).await?;
    println!("✅ Counter started: {:?}", reaction);

    // 4. Demonstrate child spawning
    println!("\n📡 Demonstrating child actor spawning:");
    let reaction = counter_handle.act(actor::CounterAction::Spawn { name: "stats".to_string() }).await?;
    println!("✅ Child spawned: {:?}", reaction);

    let reaction = counter_handle.act(actor::CounterAction::Spawn { name: "metrics".to_string() }).await?;
    println!("✅ Child spawned: {:?}", reaction);

    // 5. Test counter functionality
    println!("\n🔢 Testing counter functionality:");
    let reaction = counter_handle.act(actor::CounterAction::Increment).await?;
    println!("✅ Counter incremented: {:?}", reaction);

    let reaction = counter_handle.act(actor::CounterAction::Get).await?;
    println!("✅ Counter value: {:?}", reaction);

    // 6. Demonstrate rkyv serialization
    println!("\n🗜️  Demonstrating rkyv zero-copy serialization:");
    let action = actor::CounterAction::Increment;
    let bytes = rkyv::to_bytes::<rkyv::rancor::Error>(&action).unwrap();
    let archived = rkyv::access::<rkyv::Archived<actor::CounterAction>, rkyv::rancor::Error>(&bytes).unwrap();
    println!("✅ Serialization: {:?} → {} bytes → {:?}", action, bytes.len(), archived);

    // 7. Demonstrate hierarchical shutdown (children auto-stop)
    println!("\n🛑 Demonstrating hierarchical shutdown:");
    let reaction = counter_handle.act(actor::CounterAction::Stop).await?;
    println!("✅ Counter stopped (children auto-stopped): {:?}", reaction);

    let reaction = context_handle.act(context::Action::Standard(tamtil::StandardAction::Stop)).await?;
    println!("✅ Context stopped: {:?}", reaction);

    let reaction = platform_handle.act(platform::Action::Standard(tamtil::StandardAction::Stop)).await?;
    println!("✅ Platform stopped: {:?}", reaction);

    Ok(())
}
