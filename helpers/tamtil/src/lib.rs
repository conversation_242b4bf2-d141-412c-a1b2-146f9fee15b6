//! TAMTIL - High-performance actor system with rkyv zero-copy serialization
//!
//! Everything is an actor following the universal pattern: `actor.act(action)`
//!
//! Key features:
//! - <PERSON>'s actor pattern with tokio hidden behind platform abstraction
//! - rkyv zero-copy serialization for maximum performance
//! - Single-word naming convention throughout

use tokio::sync::{mpsc, oneshot};
use async_trait::async_trait;
use thiserror::Error;
use rkyv::{Archive, Serialize, Deserialize, rancor::Error as RancorError};

/// Core error types for TAMTIL
#[derive(Error, Debug)]
pub enum TamtilError {
    #[error("Actor not found: {id}")]
    ActorNotFound { id: String },
    #[error("Platform error: {message}")]
    Platform { message: String },
    #[error("Communication error: {message}")]
    Communication { message: String },
    #[error("Serialization error: {message}")]
    Serialization { message: String },
}

/// Result type for TAMTIL operations
pub type TamtilResult<T> = Result<T, TamtilError>;

/// Unique identifier for actors following URL-based addressing
#[derive(Debug, <PERSON>lone, PartialEq, Eq, Hash, Archive, Serialize, Deserialize)]
#[rkyv(compare(PartialEq), derive(Debug))]
pub struct ActorId(String);

impl ActorId {
    pub fn new(id: impl Into<String>) -> Self {
        Self(id.into())
    }
    
    pub fn as_str(&self) -> &str {
        &self.0
    }
}

impl From<&str> for ActorId {
    fn from(s: &str) -> Self {
        Self(s.to_string())
    }
}

impl From<String> for ActorId {
    fn from(s: String) -> Self {
        Self(s)
    }
}

/// Core trait that all TAMTIL actors must implement
#[async_trait]
pub trait Actor: Send + Sync + 'static {
    type Action: Archive + for<'a> Serialize<rkyv::api::high::HighSerializer<rkyv::util::AlignedVec, rkyv::ser::allocator::ArenaHandle<'a>, RancorError>> + Send + Sync + 'static;
    type Reaction: Archive + for<'a> Serialize<rkyv::api::high::HighSerializer<rkyv::util::AlignedVec, rkyv::ser::allocator::ArenaHandle<'a>, RancorError>> + Send + Sync + 'static;

    /// Handle an action and return a reaction
    async fn act(&mut self, action: Self::Action) -> TamtilResult<Self::Reaction>;
}

/// Message envelope for actor communication following Alice Ryhl's pattern
pub enum ActorMessage<A, R> {
    Action {
        action: A,
        respond_to: Option<oneshot::Sender<TamtilResult<R>>>,
    },
    Shutdown,
}

/// Actor task that runs independently following Alice Ryhl's pattern
pub struct ActorTask<T: Actor> {
    id: ActorId,
    actor: T,
    receiver: mpsc::Receiver<ActorMessage<T::Action, T::Reaction>>,
}

impl<T: Actor> ActorTask<T> {
    pub fn new(
        id: ActorId,
        actor: T,
        receiver: mpsc::Receiver<ActorMessage<T::Action, T::Reaction>>,
    ) -> Self {
        Self {
            id,
            actor,
            receiver,
        }
    }
    
    /// Handle a single message
    async fn handle_message(&mut self, msg: ActorMessage<T::Action, T::Reaction>) -> TamtilResult<bool> {
        match msg {
            ActorMessage::Action { action, respond_to } => {
                let result = self.actor.act(action).await;
                
                if let Some(sender) = respond_to {
                    let _ = sender.send(result);
                }
                Ok(true) // Continue running
            }
            ActorMessage::Shutdown => {
                Ok(false) // Stop running
            }
        }
    }
}

/// Run the actor task (following Alice Ryhl's pattern)
pub async fn run_actor_task<T: Actor>(mut task: ActorTask<T>) -> TamtilResult<()> {
    tracing::info!("Actor {} starting", task.id.as_str());
    
    // Main message loop
    while let Some(msg) = task.receiver.recv().await {
        match task.handle_message(msg).await {
            Ok(true) => continue,  // Keep running
            Ok(false) => break,    // Shutdown requested
            Err(e) => {
                tracing::error!("Actor {} error: {}", task.id.as_str(), e);
                break;
            }
        }
    }
    
    tracing::info!("Actor {} stopped", task.id.as_str());
    Ok(())
}

/// Typed actor handle for type-safe communication
pub struct ActorHandle<T: Actor> {
    id: ActorId,
    sender: mpsc::Sender<ActorMessage<T::Action, T::Reaction>>,
}

impl<T: Actor> ActorHandle<T> {
    pub fn new(id: ActorId, sender: mpsc::Sender<ActorMessage<T::Action, T::Reaction>>) -> Self {
        Self { id, sender }
    }
    
    /// Send an action to the actor and wait for a reaction
    pub async fn act(&self, action: T::Action) -> TamtilResult<T::Reaction> {
        let (send, recv) = oneshot::channel();
        
        let msg = ActorMessage::Action {
            action,
            respond_to: Some(send),
        };
        
        self.sender.send(msg).await
            .map_err(|_| TamtilError::Communication {
                message: "Failed to send action to actor".to_string()
            })?;
            
        recv.await
            .map_err(|_| TamtilError::Communication {
                message: "Actor task has been killed".to_string()
            })?
    }
    
    /// Get the actor's ID
    pub fn id(&self) -> &ActorId {
        &self.id
    }
    
    /// Shutdown the actor
    pub async fn shutdown(&self) -> TamtilResult<()> {
        self.sender.send(ActorMessage::Shutdown).await
            .map_err(|_| TamtilError::Communication {
                message: "Failed to send shutdown message".to_string()
            })?;
        Ok(())
    }
}

/// Platform manages the actor system and hides the underlying runtime
pub struct Platform;

impl Platform {
    /// Create a new platform
    pub fn new() -> Self {
        Self
    }
    
    /// Spawn an actor on the platform
    pub async fn spawn<T: Actor + 'static>(
        &self,
        id: impl Into<ActorId>,
        actor: T,
    ) -> TamtilResult<ActorHandle<T>> {
        let actor_id = id.into();
        let (sender, receiver) = mpsc::channel(32);
        
        // Create the actor task
        let task = ActorTask::new(
            actor_id.clone(),
            actor,
            receiver,
        );
        
        // Spawn the task
        tokio::spawn(run_actor_task(task));
        
        // Create and return the handle
        let handle = ActorHandle::new(actor_id, sender);
        
        Ok(handle)
    }
}

impl Default for Platform {
    fn default() -> Self {
        Self::new()
    }
}

// ============================================================================
// EXAMPLE IMPLEMENTATION - Counter Actor
// ============================================================================

/// Example counter action using rkyv
#[derive(Debug, Clone, Archive, Serialize, Deserialize)]
#[rkyv(compare(PartialEq), derive(Debug))]
pub enum CounterAction {
    Start,
    Stop,
    Increment,
    Get,
}

/// Example counter reaction using rkyv
#[derive(Debug, Clone, Archive, Serialize, Deserialize)]
#[rkyv(compare(PartialEq), derive(Debug))]
pub enum CounterReaction {
    Started,
    Stopped,
    Incremented { new_count: u32 },
    Count { value: u32 },
}

/// Example counter actor implementation
pub struct CounterActor {
    id: ActorId,
    count: u32,
}

impl CounterActor {
    pub fn new(id: impl Into<ActorId>) -> Self {
        Self {
            id: id.into(),
            count: 0,
        }
    }
}

#[async_trait]
impl Actor for CounterActor {
    type Action = CounterAction;
    type Reaction = CounterReaction;

    async fn act(&mut self, action: Self::Action) -> TamtilResult<Self::Reaction> {
        match action {
            CounterAction::Start => {
                tracing::info!("Counter actor {} starting", self.id.as_str());
                Ok(CounterReaction::Started)
            }

            CounterAction::Stop => {
                tracing::info!("Counter actor {} stopping", self.id.as_str());
                Ok(CounterReaction::Stopped)
            }

            CounterAction::Increment => {
                self.count += 1;
                tracing::debug!("Counter actor {} incremented to {}", self.id.as_str(), self.count);
                Ok(CounterReaction::Incremented { new_count: self.count })
            }

            CounterAction::Get => {
                Ok(CounterReaction::Count { value: self.count })
            }
        }
    }
}

// ============================================================================
// TESTS
// ============================================================================

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_counter_actor() {
        // Initialize tracing for test output
        let _ = tracing_subscriber::fmt::try_init();

        // Create platform
        let platform = Platform::new();

        // Spawn counter actor
        let counter = CounterActor::new("test-counter");
        let handle = platform.spawn("test-counter", counter).await.unwrap();

        // Test start
        let reaction = handle.act(CounterAction::Start).await.unwrap();
        assert!(matches!(reaction, CounterReaction::Started));

        // Test increment
        let reaction = handle.act(CounterAction::Increment).await.unwrap();
        assert!(matches!(reaction, CounterReaction::Incremented { new_count: 1 }));

        // Test get
        let reaction = handle.act(CounterAction::Get).await.unwrap();
        assert!(matches!(reaction, CounterReaction::Count { value: 1 }));

        // Test another increment
        let reaction = handle.act(CounterAction::Increment).await.unwrap();
        assert!(matches!(reaction, CounterReaction::Incremented { new_count: 2 }));

        // Test get again
        let reaction = handle.act(CounterAction::Get).await.unwrap();
        assert!(matches!(reaction, CounterReaction::Count { value: 2 }));

        // Test stop
        let reaction = handle.act(CounterAction::Stop).await.unwrap();
        assert!(matches!(reaction, CounterReaction::Stopped));

        println!("✅ All tests passed! TAMTIL MVP is working with rkyv serialization.");
    }

    #[tokio::test]
    async fn test_rkyv_serialization() {
        // Test that our actions and reactions can be serialized with rkyv
        let action = CounterAction::Increment;
        let bytes = rkyv::to_bytes::<RancorError>(&action).unwrap();
        let archived = rkyv::access::<rkyv::Archived<CounterAction>, RancorError>(&bytes).unwrap();
        assert_eq!(archived, &action);

        let reaction = CounterReaction::Incremented { new_count: 42 };
        let bytes = rkyv::to_bytes::<RancorError>(&reaction).unwrap();
        let archived = rkyv::access::<rkyv::Archived<CounterReaction>, RancorError>(&bytes).unwrap();
        assert_eq!(archived, &reaction);

        println!("✅ rkyv serialization working correctly!");
    }
}
