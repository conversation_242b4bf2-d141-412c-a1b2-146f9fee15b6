//! TAMTIL - High-performance actor system with rkyv zero-copy serialization
//!
//! Everything is an actor following the universal pattern: `actor.act(action)`
//!
//! Key features:
//! - <PERSON>'s actor pattern with tokio hidden behind platform abstraction
//! - rkyv zero-copy serialization for maximum performance
//! - Single-word naming convention throughout
//! - Platform spawns context actors, context actors spawn regular actors
//! - Standard actions/reactions for lifecycle management

use tokio::sync::{mpsc, oneshot};
use async_trait::async_trait;
use thiserror::Error;
use rkyv::{Archive, Serialize, Deserialize, rancor::Error as RancorError};
use std::collections::HashMap;

/// Core error types for TAMTIL
#[derive(Error, Debug)]
pub enum TamtilError {
    #[error("Actor not found: {id}")]
    ActorNotFound { id: String },
    #[error("Platform error: {message}")]
    Platform { message: String },
    #[error("Communication error: {message}")]
    Communication { message: String },
    #[error("Serialization error: {message}")]
    Serialization { message: String },
}

/// Result type for TAMTIL operations
pub type TamtilResult<T> = Result<T, TamtilError>;

/// Unique identifier for actors following URL-based addressing
#[derive(Debug, Clone, PartialEq, Eq, Hash, Archive, Serialize, Deserialize)]
#[rkyv(compare(PartialEq), derive(Debug))]
pub struct ActorId(String);

impl ActorId {
    pub fn new(id: impl Into<String>) -> Self {
        Self(id.into())
    }
    
    pub fn as_str(&self) -> &str {
        &self.0
    }
}

impl From<&str> for ActorId {
    fn from(s: &str) -> Self {
        Self(s.to_string())
    }
}

impl From<String> for ActorId {
    fn from(s: String) -> Self {
        Self(s)
    }
}

// ============================================================================
// STANDARD ACTIONS AND REACTIONS
// ============================================================================

/// Standard actions that all actors support
#[derive(Debug, Clone, Archive, Serialize, Deserialize)]
#[rkyv(compare(PartialEq), derive(Debug))]
pub enum StandardAction {
    Start,
    Stop,
    Shutdown,
}

/// Standard reactions that all actors can produce
#[derive(Debug, Clone, Archive, Serialize, Deserialize)]
#[rkyv(compare(PartialEq), derive(Debug))]
pub enum StandardReaction {
    Started,
    Stopped,
    Shutdown,
}

/// Actions for context actors to manage regular actors
#[derive(Debug, Clone, Archive, Serialize, Deserialize)]
#[rkyv(compare(PartialEq), derive(Debug))]
pub enum ContextAction {
    Standard(StandardAction),
    StartActor { id: ActorId },
    StopActor { id: ActorId },
}

/// Reactions from context actors
#[derive(Debug, Clone, Archive, Serialize, Deserialize)]
#[rkyv(compare(PartialEq), derive(Debug))]
pub enum ContextReaction {
    Standard(StandardReaction),
    ActorStarted { id: ActorId },
    ActorStopped { id: ActorId },
    ActorNotFound { id: ActorId },
}

/// Actions for platform to manage context actors
#[derive(Debug, Clone, Archive, Serialize, Deserialize)]
#[rkyv(compare(PartialEq), derive(Debug))]
pub enum PlatformAction {
    Standard(StandardAction),
    StartContext { id: ActorId },
    StopContext { id: ActorId },
}

/// Reactions from platform
#[derive(Debug, Clone, Archive, Serialize, Deserialize)]
#[rkyv(compare(PartialEq), derive(Debug))]
pub enum PlatformReaction {
    Standard(StandardReaction),
    ContextStarted { id: ActorId },
    ContextStopped { id: ActorId },
    ContextNotFound { id: ActorId },
}

// ============================================================================
// ACTORS REGISTRY - Enables actors.actor(id).act(action) pattern
// ============================================================================

/// Universal action envelope for inter-actor communication using rkyv
#[derive(Debug, Clone, Archive, Serialize, Deserialize)]
#[rkyv(compare(PartialEq), derive(Debug))]
pub enum UniversalAction {
    Platform(PlatformAction),
    Context(ContextAction),
    Counter(CounterAction),
}

/// Universal reaction envelope for inter-actor communication using rkyv
#[derive(Debug, Clone, Archive, Serialize, Deserialize)]
#[rkyv(compare(PartialEq), derive(Debug))]
pub enum UniversalReaction {
    Platform(PlatformReaction),
    Context(ContextReaction),
    Counter(CounterReaction),
}

/// Simple actors registry for demonstration
/// In a real implementation, this would be more sophisticated
#[derive(Clone, Default)]
pub struct Actors;

impl Actors {
    pub fn new() -> Self {
        Self
    }

    /// Get an actor proxy for communication
    /// For now, this is a placeholder that demonstrates the API
    pub fn actor(&self, id: impl Into<ActorId>) -> ActorProxy {
        ActorProxy {
            id: id.into(),
        }
    }
}

/// Proxy for actor communication - provides the actors.actor(id).act(action) interface
pub struct ActorProxy {
    id: ActorId,
}

impl ActorProxy {
    /// Send a platform action to the actor (placeholder)
    pub async fn act_platform(&self, _action: PlatformAction) -> TamtilResult<PlatformReaction> {
        // In a real implementation, this would route to the actual actor
        tracing::debug!("Would send platform action to {}", self.id.as_str());
        Err(TamtilError::ActorNotFound { id: self.id.as_str().to_string() })
    }

    /// Send a context action to the actor (placeholder)
    pub async fn act_context(&self, _action: ContextAction) -> TamtilResult<ContextReaction> {
        // In a real implementation, this would route to the actual actor
        tracing::debug!("Would send context action to {}", self.id.as_str());
        Err(TamtilError::ActorNotFound { id: self.id.as_str().to_string() })
    }

    /// Send a counter action to the actor (placeholder)
    pub async fn act_counter(&self, _action: CounterAction) -> TamtilResult<CounterReaction> {
        // In a real implementation, this would route to the actual actor
        tracing::debug!("Would send counter action to {}", self.id.as_str());
        Err(TamtilError::ActorNotFound { id: self.id.as_str().to_string() })
    }

    /// Get the actor ID
    pub fn id(&self) -> &ActorId {
        &self.id
    }
}

/// Core trait that all TAMTIL actors must implement
#[async_trait]
pub trait Actor: Send + Sync + 'static {
    type Action: Archive + for<'a> Serialize<rkyv::api::high::HighSerializer<rkyv::util::AlignedVec, rkyv::ser::allocator::ArenaHandle<'a>, RancorError>> + Send + Sync + 'static;
    type Reaction: Archive + for<'a> Serialize<rkyv::api::high::HighSerializer<rkyv::util::AlignedVec, rkyv::ser::allocator::ArenaHandle<'a>, RancorError>> + Send + Sync + 'static;

    /// Handle an action and return a reaction
    /// Actors can communicate with other actors via the actors parameter
    async fn act(&mut self, action: Self::Action, actors: &Actors) -> TamtilResult<Self::Reaction>;
}

// ============================================================================
// HANDLER PATTERN - Only exposes act() and new()
// ============================================================================

/// Handler provides a clean interface that only exposes act() and new()
/// This hides the complexity of the actor task/handle pattern
pub struct Handler<T: Actor> {
    handle: ActorHandle<T>,
}

/// Specialized handlers for each actor type
pub struct PlatformHandler {
    handle: ActorHandle<PlatformActor>,
}

impl PlatformHandler {
    pub async fn new(id: impl Into<ActorId>, actor: PlatformActor) -> TamtilResult<Self> {
        let mut platform = Platform::new();
        let handle = platform.spawn_platform(id, actor).await?;
        Ok(Self { handle })
    }

    pub async fn act(&self, action: PlatformAction) -> TamtilResult<PlatformReaction> {
        self.handle.act(action).await
    }
}

pub struct ContextHandler {
    handle: ActorHandle<ContextActor>,
}

impl ContextHandler {
    pub async fn new(id: impl Into<ActorId>, actor: ContextActor) -> TamtilResult<Self> {
        let mut platform = Platform::new();
        let handle = platform.spawn_context(id, actor).await?;
        Ok(Self { handle })
    }

    pub async fn act(&self, action: ContextAction) -> TamtilResult<ContextReaction> {
        self.handle.act(action).await
    }
}

pub struct CounterHandler {
    handle: ActorHandle<CounterActor>,
}

impl CounterHandler {
    pub async fn new(id: impl Into<ActorId>, actor: CounterActor) -> TamtilResult<Self> {
        let mut platform = Platform::new();
        let handle = platform.spawn_counter(id, actor).await?;
        Ok(Self { handle })
    }

    pub async fn act(&self, action: CounterAction) -> TamtilResult<CounterReaction> {
        self.handle.act(action).await
    }
}

/// Message envelope for actor communication following Alice Ryhl's pattern
pub enum ActorMessage<A, R> {
    Action {
        action: A,
        respond_to: Option<oneshot::Sender<TamtilResult<R>>>,
    },
    Shutdown,
}

/// Actor task that runs independently following Alice Ryhl's pattern
pub struct ActorTask<T: Actor> {
    id: ActorId,
    actor: T,
    receiver: mpsc::Receiver<ActorMessage<T::Action, T::Reaction>>,
    actors: Actors,
}

impl<T: Actor> ActorTask<T> {
    pub fn new(
        id: ActorId,
        actor: T,
        receiver: mpsc::Receiver<ActorMessage<T::Action, T::Reaction>>,
        actors: Actors,
    ) -> Self {
        Self {
            id,
            actor,
            receiver,
            actors,
        }
    }

    /// Handle a single message
    async fn handle_message(&mut self, msg: ActorMessage<T::Action, T::Reaction>) -> TamtilResult<bool> {
        match msg {
            ActorMessage::Action { action, respond_to } => {
                let result = self.actor.act(action, &self.actors).await;

                if let Some(sender) = respond_to {
                    let _ = sender.send(result);
                }
                Ok(true) // Continue running
            }
            ActorMessage::Shutdown => {
                Ok(false) // Stop running
            }
        }
    }
}

/// Run the actor task (following Alice Ryhl's pattern)
pub async fn run_actor_task<T: Actor>(mut task: ActorTask<T>) -> TamtilResult<()> {
    tracing::info!("Actor {} starting", task.id.as_str());
    
    // Main message loop
    while let Some(msg) = task.receiver.recv().await {
        match task.handle_message(msg).await {
            Ok(true) => continue,  // Keep running
            Ok(false) => break,    // Shutdown requested
            Err(e) => {
                tracing::error!("Actor {} error: {}", task.id.as_str(), e);
                break;
            }
        }
    }
    
    tracing::info!("Actor {} stopped", task.id.as_str());
    Ok(())
}

/// Typed actor handle for type-safe communication
#[derive(Clone)]
pub struct ActorHandle<T: Actor> {
    id: ActorId,
    sender: mpsc::Sender<ActorMessage<T::Action, T::Reaction>>,
}

impl<T: Actor> ActorHandle<T> {
    pub fn new(id: ActorId, sender: mpsc::Sender<ActorMessage<T::Action, T::Reaction>>) -> Self {
        Self { id, sender }
    }
    
    /// Send an action to the actor and wait for a reaction
    pub async fn act(&self, action: T::Action) -> TamtilResult<T::Reaction> {
        let (send, recv) = oneshot::channel();
        
        let msg = ActorMessage::Action {
            action,
            respond_to: Some(send),
        };
        
        self.sender.send(msg).await
            .map_err(|_| TamtilError::Communication {
                message: "Failed to send action to actor".to_string()
            })?;
            
        recv.await
            .map_err(|_| TamtilError::Communication {
                message: "Actor task has been killed".to_string()
            })?
    }
    
    /// Get the actor's ID
    pub fn id(&self) -> &ActorId {
        &self.id
    }
    
    /// Shutdown the actor
    pub async fn shutdown(&self) -> TamtilResult<()> {
        self.sender.send(ActorMessage::Shutdown).await
            .map_err(|_| TamtilError::Communication {
                message: "Failed to send shutdown message".to_string()
            })?;
        Ok(())
    }
}

// ============================================================================
// PLATFORM ACTOR - Spawns and manages context actors
// ============================================================================

/// Platform actor that manages context actors
pub struct PlatformActor {
    id: ActorId,
    contexts: HashMap<ActorId, ActorHandle<ContextActor>>,
}

impl PlatformActor {
    pub fn new(id: impl Into<ActorId>) -> Self {
        Self {
            id: id.into(),
            contexts: HashMap::new(),
        }
    }
}

#[async_trait]
impl Actor for PlatformActor {
    type Action = PlatformAction;
    type Reaction = PlatformReaction;

    async fn act(&mut self, action: Self::Action, actors: &Actors) -> TamtilResult<Self::Reaction> {
        match action {
            PlatformAction::Standard(StandardAction::Start) => {
                tracing::info!("Platform {} starting", self.id.as_str());
                Ok(PlatformReaction::Standard(StandardReaction::Started))
            }

            PlatformAction::Standard(StandardAction::Stop) => {
                tracing::info!("Platform {} stopping", self.id.as_str());
                // Stop all contexts using actors registry
                for (context_id, _) in &self.contexts {
                    let _ = actors.actor(context_id.clone())
                        .act_context(ContextAction::Standard(StandardAction::Stop))
                        .await;
                }
                self.contexts.clear();
                Ok(PlatformReaction::Standard(StandardReaction::Stopped))
            }

            PlatformAction::Standard(StandardAction::Shutdown) => {
                tracing::info!("Platform {} shutting down", self.id.as_str());
                // Shutdown all contexts using actors registry
                for (context_id, context_handle) in &self.contexts {
                    let _ = actors.actor(context_id.clone())
                        .act_context(ContextAction::Standard(StandardAction::Shutdown))
                        .await;
                    let _ = context_handle.shutdown().await;
                }
                self.contexts.clear();
                Ok(PlatformReaction::Standard(StandardReaction::Shutdown))
            }

            PlatformAction::StartContext { id } => {
                if self.contexts.contains_key(&id) {
                    return Ok(PlatformReaction::ContextStarted { id });
                }

                let context = ContextActor::new(id.clone());
                let mut platform = Platform::new();
                let handle = platform.spawn_context(id.clone(), context).await?;

                // Start the context using actors registry
                let _ = actors.actor(id.clone())
                    .act_context(ContextAction::Standard(StandardAction::Start))
                    .await;

                self.contexts.insert(id.clone(), handle);
                tracing::info!("Platform {} started context {}", self.id.as_str(), id.as_str());
                Ok(PlatformReaction::ContextStarted { id })
            }

            PlatformAction::StopContext { id } => {
                if let Some(handle) = self.contexts.remove(&id) {
                    let _ = actors.actor(id.clone())
                        .act_context(ContextAction::Standard(StandardAction::Stop))
                        .await;
                    let _ = handle.shutdown().await;
                    tracing::info!("Platform {} stopped context {}", self.id.as_str(), id.as_str());
                    Ok(PlatformReaction::ContextStopped { id })
                } else {
                    Ok(PlatformReaction::ContextNotFound { id })
                }
            }
        }
    }
}

// ============================================================================
// CONTEXT ACTOR - Spawns and manages regular actors
// ============================================================================

/// Context actor that manages regular actors
pub struct ContextActor {
    id: ActorId,
    counter_actors: HashMap<ActorId, ActorHandle<CounterActor>>, // Type-safe actor handles
}

impl ContextActor {
    pub fn new(id: impl Into<ActorId>) -> Self {
        Self {
            id: id.into(),
            counter_actors: HashMap::new(),
        }
    }
}

#[async_trait]
impl Actor for ContextActor {
    type Action = ContextAction;
    type Reaction = ContextReaction;

    async fn act(&mut self, action: Self::Action, actors: &Actors) -> TamtilResult<Self::Reaction> {
        match action {
            ContextAction::Standard(StandardAction::Start) => {
                tracing::info!("Context {} starting", self.id.as_str());
                Ok(ContextReaction::Standard(StandardReaction::Started))
            }

            ContextAction::Standard(StandardAction::Stop) => {
                tracing::info!("Context {} stopping", self.id.as_str());
                // Stop all actors using actors registry
                for (actor_id, _) in &self.counter_actors {
                    let _ = actors.actor(actor_id.clone())
                        .act_counter(CounterAction::Stop)
                        .await;
                }
                self.counter_actors.clear();
                Ok(ContextReaction::Standard(StandardReaction::Stopped))
            }

            ContextAction::Standard(StandardAction::Shutdown) => {
                tracing::info!("Context {} shutting down", self.id.as_str());
                // Shutdown all actors using actors registry
                for (actor_id, _) in &self.counter_actors {
                    let _ = actors.actor(actor_id.clone())
                        .act_counter(CounterAction::Stop)
                        .await;
                }
                self.counter_actors.clear();
                Ok(ContextReaction::Standard(StandardReaction::Shutdown))
            }

            ContextAction::StartActor { id } => {
                // For this example, we'll create a counter actor
                // In a real implementation, this would be more flexible
                if self.counter_actors.contains_key(&id) {
                    return Ok(ContextReaction::ActorStarted { id });
                }

                let counter = CounterActor::new(id.clone());
                let mut platform = Platform::new();
                let handle = platform.spawn_counter(id.clone(), counter).await?;

                // Start the actor using actors registry
                let _ = actors.actor(id.clone())
                    .act_counter(CounterAction::Start)
                    .await;

                self.counter_actors.insert(id.clone(), handle);
                tracing::info!("Context {} started actor {}", self.id.as_str(), id.as_str());
                Ok(ContextReaction::ActorStarted { id })
            }

            ContextAction::StopActor { id } => {
                if self.counter_actors.remove(&id).is_some() {
                    let _ = actors.actor(id.clone())
                        .act_counter(CounterAction::Stop)
                        .await;
                    tracing::info!("Context {} stopped actor {}", self.id.as_str(), id.as_str());
                    Ok(ContextReaction::ActorStopped { id })
                } else {
                    Ok(ContextReaction::ActorNotFound { id })
                }
            }
        }
    }
}

/// Platform manages the actor system and hides the underlying runtime
pub struct Platform {
    actors: Actors,
}

impl Platform {
    /// Create a new platform
    pub fn new() -> Self {
        Self {
            actors: Actors::new(),
        }
    }

    /// Spawn a platform actor
    pub async fn spawn_platform(
        &mut self,
        id: impl Into<ActorId>,
        actor: PlatformActor,
    ) -> TamtilResult<ActorHandle<PlatformActor>> {
        let actor_id = id.into();
        let (sender, receiver) = mpsc::channel(32);

        let task = ActorTask::new(actor_id.clone(), actor, receiver, self.actors.clone());
        tokio::spawn(run_actor_task(task));

        let handle = ActorHandle::new(actor_id, sender);
        Ok(handle)
    }

    /// Spawn a context actor
    pub async fn spawn_context(
        &mut self,
        id: impl Into<ActorId>,
        actor: ContextActor,
    ) -> TamtilResult<ActorHandle<ContextActor>> {
        let actor_id = id.into();
        let (sender, receiver) = mpsc::channel(32);

        let task = ActorTask::new(actor_id.clone(), actor, receiver, self.actors.clone());
        tokio::spawn(run_actor_task(task));

        let handle = ActorHandle::new(actor_id, sender);
        Ok(handle)
    }

    /// Spawn a counter actor
    pub async fn spawn_counter(
        &mut self,
        id: impl Into<ActorId>,
        actor: CounterActor,
    ) -> TamtilResult<ActorHandle<CounterActor>> {
        let actor_id = id.into();
        let (sender, receiver) = mpsc::channel(32);

        let task = ActorTask::new(actor_id.clone(), actor, receiver, self.actors.clone());
        tokio::spawn(run_actor_task(task));

        let handle = ActorHandle::new(actor_id, sender);
        Ok(handle)
    }

    /// Get the actors registry for inter-actor communication
    pub fn actors(&self) -> &Actors {
        &self.actors
    }
}

impl Default for Platform {
    fn default() -> Self {
        Self::new()
    }
}

// ============================================================================
// EXAMPLE IMPLEMENTATION - Counter Actor
// ============================================================================

/// Example counter action using rkyv
#[derive(Debug, Clone, Archive, Serialize, Deserialize)]
#[rkyv(compare(PartialEq), derive(Debug))]
pub enum CounterAction {
    Start,
    Stop,
    Increment,
    Get,
}

/// Example counter reaction using rkyv
#[derive(Debug, Clone, Archive, Serialize, Deserialize)]
#[rkyv(compare(PartialEq), derive(Debug))]
pub enum CounterReaction {
    Started,
    Stopped,
    Incremented { new_count: u32 },
    Count { value: u32 },
}

/// Example counter actor implementation
pub struct CounterActor {
    id: ActorId,
    count: u32,
}

impl CounterActor {
    pub fn new(id: impl Into<ActorId>) -> Self {
        Self {
            id: id.into(),
            count: 0,
        }
    }
}

#[async_trait]
impl Actor for CounterActor {
    type Action = CounterAction;
    type Reaction = CounterReaction;

    async fn act(&mut self, action: Self::Action, actors: &Actors) -> TamtilResult<Self::Reaction> {
        match action {
            CounterAction::Start => {
                tracing::info!("Counter actor {} starting", self.id.as_str());
                Ok(CounterReaction::Started)
            }

            CounterAction::Stop => {
                tracing::info!("Counter actor {} stopping", self.id.as_str());
                Ok(CounterReaction::Stopped)
            }

            CounterAction::Increment => {
                self.count += 1;
                tracing::debug!("Counter actor {} incremented to {}", self.id.as_str(), self.count);

                // Example of inter-actor communication: could notify other actors
                // For now, just demonstrate the pattern is available
                let _actors_available = actors;

                Ok(CounterReaction::Incremented { new_count: self.count })
            }

            CounterAction::Get => {
                Ok(CounterReaction::Count { value: self.count })
            }
        }
    }
}

// ============================================================================
// EXAMPLE MODULE - Demonstrates platform->context->actor hierarchy with URL addressing
// ============================================================================

pub mod example {
    use super::*;

    /// Example demonstrating URL-based addressing: platform.com/context_name/context_id/actor_name/actor_id
    pub async fn run_url_addressing_example() -> TamtilResult<()> {
        // Initialize tracing
        let _ = tracing_subscriber::fmt::try_init();

        println!("🚀 Starting TAMTIL URL addressing example...");
        println!("📍 URL format: platform.com/context_name/context_id/actor_name/actor_id");

        // 1. Create platform actor with URL-based ID
        let platform_id = ActorId::new("platform.com");
        let platform_actor = PlatformActor::new(platform_id.clone());
        let platform_handler = PlatformHandler::new(platform_id.clone(), platform_actor).await?;

        // Start the platform
        let reaction = platform_handler.act(PlatformAction::Standard(StandardAction::Start)).await?;
        println!("✅ Platform started at {}: {:?}", platform_id.as_str(), reaction);

        // 2. Create context actor with URL-based ID
        let context_id = ActorId::new("platform.com/web/main");
        let context_actor = ContextActor::new(context_id.clone());
        let context_handler = ContextHandler::new(context_id.clone(), context_actor).await?;

        // Start the context
        let reaction = context_handler.act(ContextAction::Standard(StandardAction::Start)).await?;
        println!("✅ Context started at {}: {:?}", context_id.as_str(), reaction);

        // 3. Create counter actor with URL-based ID
        let counter_id = ActorId::new("platform.com/web/main/counter/user-stats");
        let counter_actor = CounterActor::new(counter_id.clone());
        let counter_handler = CounterHandler::new(counter_id.clone(), counter_actor).await?;

        // Start and test the counter
        let reaction = counter_handler.act(CounterAction::Start).await?;
        println!("✅ Counter started at {}: {:?}", counter_id.as_str(), reaction);

        let reaction = counter_handler.act(CounterAction::Increment).await?;
        println!("✅ Counter incremented: {:?}", reaction);

        let reaction = counter_handler.act(CounterAction::Get).await?;
        println!("✅ Counter value: {:?}", reaction);

        // 4. Demonstrate inter-actor communication pattern
        println!("📡 Demonstrating actors.actor(id).act(action) pattern...");

        // In a real implementation, actors would communicate like this:
        // let other_counter_id = ActorId::new("platform.com/web/main/counter/global-stats");
        // let reaction = actors.actor(other_counter_id).act_counter(CounterAction::Get).await?;

        println!("💡 Actors can communicate using: actors.actor(\"platform.com/web/main/counter/other\").act_counter(action)");

        // 5. Stop everything
        let reaction = counter_handler.act(CounterAction::Stop).await?;
        println!("✅ Counter stopped: {:?}", reaction);

        let reaction = context_handler.act(ContextAction::Standard(StandardAction::Stop)).await?;
        println!("✅ Context stopped: {:?}", reaction);

        let reaction = platform_handler.act(PlatformAction::Standard(StandardAction::Stop)).await?;
        println!("✅ Platform stopped: {:?}", reaction);

        println!("🎉 TAMTIL URL addressing example completed successfully!");

        Ok(())
    }

    /// Example showing inter-actor communication with rkyv serialization
    pub async fn run_inter_actor_communication_example() -> TamtilResult<()> {
        println!("🔧 Testing inter-actor communication with rkyv...");

        // Create two counter actors
        let counter1 = CounterActor::new("platform.com/app/main/counter/stats");
        let handler1 = CounterHandler::new("platform.com/app/main/counter/stats", counter1).await?;

        let counter2 = CounterActor::new("platform.com/app/main/counter/metrics");
        let handler2 = CounterHandler::new("platform.com/app/main/counter/metrics", counter2).await?;

        // Start both counters
        let _ = handler1.act(CounterAction::Start).await?;
        let _ = handler2.act(CounterAction::Start).await?;

        // Test rkyv serialization
        let action = CounterAction::Increment;
        let bytes = rkyv::to_bytes::<RancorError>(&action).unwrap();
        let archived = rkyv::access::<rkyv::Archived<CounterAction>, RancorError>(&bytes).unwrap();
        println!("✅ rkyv serialization: {:?} -> {} bytes -> {:?}", action, bytes.len(), archived);

        // Increment both counters
        let reaction1 = handler1.act(CounterAction::Increment).await?;
        let reaction2 = handler2.act(CounterAction::Increment).await?;

        println!("✅ Counter 1: {:?}", reaction1);
        println!("✅ Counter 2: {:?}", reaction2);

        // Get values
        let value1 = handler1.act(CounterAction::Get).await?;
        let value2 = handler2.act(CounterAction::Get).await?;

        println!("✅ Final values - Counter 1: {:?}, Counter 2: {:?}", value1, value2);

        // Stop both
        let _ = handler1.act(CounterAction::Stop).await?;
        let _ = handler2.act(CounterAction::Stop).await?;

        println!("✅ Inter-actor communication example completed!");

        Ok(())
    }
}

// ============================================================================
// TESTS
// ============================================================================

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_counter_actor() {
        // Initialize tracing for test output
        let _ = tracing_subscriber::fmt::try_init();

        // Create counter using new handler pattern
        let counter = CounterActor::new("test-counter");
        let handler = CounterHandler::new("test-counter", counter).await.unwrap();

        // Test start
        let reaction = handler.act(CounterAction::Start).await.unwrap();
        assert!(matches!(reaction, CounterReaction::Started));

        // Test increment
        let reaction = handler.act(CounterAction::Increment).await.unwrap();
        assert!(matches!(reaction, CounterReaction::Incremented { new_count: 1 }));

        // Test get
        let reaction = handler.act(CounterAction::Get).await.unwrap();
        assert!(matches!(reaction, CounterReaction::Count { value: 1 }));

        // Test another increment
        let reaction = handler.act(CounterAction::Increment).await.unwrap();
        assert!(matches!(reaction, CounterReaction::Incremented { new_count: 2 }));

        // Test get again
        let reaction = handler.act(CounterAction::Get).await.unwrap();
        assert!(matches!(reaction, CounterReaction::Count { value: 2 }));

        // Test stop
        let reaction = handler.act(CounterAction::Stop).await.unwrap();
        assert!(matches!(reaction, CounterReaction::Stopped));

        println!("✅ All tests passed! TAMTIL MVP is working with rkyv serialization and actors parameter.");
    }

    #[tokio::test]
    async fn test_url_addressing() {
        // Test URL-based actor IDs
        let platform_id = ActorId::new("platform.com");
        let context_id = ActorId::new("platform.com/web/main");
        let actor_id = ActorId::new("platform.com/web/main/counter/user-stats");

        assert_eq!(platform_id.as_str(), "platform.com");
        assert_eq!(context_id.as_str(), "platform.com/web/main");
        assert_eq!(actor_id.as_str(), "platform.com/web/main/counter/user-stats");

        println!("✅ URL-based addressing working correctly!");
    }

    #[tokio::test]
    async fn test_platform_context_hierarchy() {
        let _ = tracing_subscriber::fmt::try_init();

        // Create platform
        let platform_actor = PlatformActor::new("platform.com");
        let platform_handler = PlatformHandler::new("platform.com", platform_actor).await.unwrap();

        // Start platform
        let reaction = platform_handler.act(PlatformAction::Standard(StandardAction::Start)).await.unwrap();
        assert!(matches!(reaction, PlatformReaction::Standard(StandardReaction::Started)));

        // Create context
        let context_actor = ContextActor::new("platform.com/web/main");
        let context_handler = ContextHandler::new("platform.com/web/main", context_actor).await.unwrap();

        // Start context
        let reaction = context_handler.act(ContextAction::Standard(StandardAction::Start)).await.unwrap();
        assert!(matches!(reaction, ContextReaction::Standard(StandardReaction::Started)));

        println!("✅ Platform->Context hierarchy working!");
    }

    #[tokio::test]
    async fn test_rkyv_serialization() {
        // Test that our actions and reactions can be serialized with rkyv
        let action = CounterAction::Increment;
        let bytes = rkyv::to_bytes::<RancorError>(&action).unwrap();
        let archived = rkyv::access::<rkyv::Archived<CounterAction>, RancorError>(&bytes).unwrap();
        assert_eq!(archived, &action);

        let reaction = CounterReaction::Incremented { new_count: 42 };
        let bytes = rkyv::to_bytes::<RancorError>(&reaction).unwrap();
        let archived = rkyv::access::<rkyv::Archived<CounterReaction>, RancorError>(&bytes).unwrap();
        assert_eq!(archived, &reaction);

        println!("✅ rkyv serialization working correctly!");
    }
}
