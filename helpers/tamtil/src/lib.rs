//! TAMTIL - High-performance actor system with rkyv zero-copy serialization
//!
//! Everything is an actor following the universal pattern: `actor.act(action)`
//!
//! Key features:
//! - <PERSON>'s actor pattern with tokio hidden behind platform abstraction
//! - rkyv zero-copy serialization for maximum performance
//! - Single-word naming convention throughout
//! - Platform spawns context actors, context actors spawn regular actors
//! - Standard actions/reactions for lifecycle management

use tokio::sync::{mpsc, oneshot};
use async_trait::async_trait;
use thiserror::Error;
use rkyv::{Archive, Serialize, Deserialize, rancor::Error as RancorError};
use std::collections::HashMap;

/// Core error types for TAMTIL
#[derive(Error, Debug)]
pub enum TamtilError {
    #[error("Actor not found: {id}")]
    ActorNotFound { id: String },
    #[error("Platform error: {message}")]
    Platform { message: String },
    #[error("Communication error: {message}")]
    Communication { message: String },
    #[error("Serialization error: {message}")]
    Serialization { message: String },
}

/// Result type for TAMTIL operations
pub type TamtilResult<T> = Result<T, TamtilError>;

/// Unique identifier for actors following URL-based addressing
#[derive(Debug, Clone, PartialEq, Eq, Hash, Archive, Serialize, Deserialize)]
#[rkyv(compare(PartialEq), derive(Debug))]
pub struct ActorId(String);

impl ActorId {
    pub fn new(id: impl Into<String>) -> Self {
        Self(id.into())
    }
    
    pub fn as_str(&self) -> &str {
        &self.0
    }
}

impl From<&str> for ActorId {
    fn from(s: &str) -> Self {
        Self(s.to_string())
    }
}

impl From<String> for ActorId {
    fn from(s: String) -> Self {
        Self(s)
    }
}

// ============================================================================
// STANDARD ACTIONS AND REACTIONS
// ============================================================================

/// Standard actions that all actors support
#[derive(Debug, Clone, Archive, Serialize, Deserialize)]
#[rkyv(compare(PartialEq), derive(Debug))]
pub enum StandardAction {
    Start,
    Stop,
    Shutdown,
}

/// Standard reactions that all actors can produce
#[derive(Debug, Clone, Archive, Serialize, Deserialize)]
#[rkyv(compare(PartialEq), derive(Debug))]
pub enum StandardReaction {
    Started,
    Stopped,
    Shutdown,
}

/// Actions for context actors to manage regular actors
#[derive(Debug, Clone, Archive, Serialize, Deserialize)]
#[rkyv(compare(PartialEq), derive(Debug))]
pub enum ContextAction {
    Standard(StandardAction),
    StartActor { id: ActorId },
    StopActor { id: ActorId },
}

/// Reactions from context actors
#[derive(Debug, Clone, Archive, Serialize, Deserialize)]
#[rkyv(compare(PartialEq), derive(Debug))]
pub enum ContextReaction {
    Standard(StandardReaction),
    ActorStarted { id: ActorId },
    ActorStopped { id: ActorId },
    ActorNotFound { id: ActorId },
}

/// Actions for platform to manage context actors
#[derive(Debug, Clone, Archive, Serialize, Deserialize)]
#[rkyv(compare(PartialEq), derive(Debug))]
pub enum PlatformAction {
    Standard(StandardAction),
    StartContext { id: ActorId },
    StopContext { id: ActorId },
}

/// Reactions from platform
#[derive(Debug, Clone, Archive, Serialize, Deserialize)]
#[rkyv(compare(PartialEq), derive(Debug))]
pub enum PlatformReaction {
    Standard(StandardReaction),
    ContextStarted { id: ActorId },
    ContextStopped { id: ActorId },
    ContextNotFound { id: ActorId },
}

// ============================================================================
// ACTORS REGISTRY - Enables actors.actor(id).act(action) pattern
// ============================================================================

/// Actors registry that enables inter-actor communication
/// Supports URL-based addressing: platform.com/context_name/context_id/actor_name/actor_id
#[derive(Clone)]
pub struct Actors {
    registry: HashMap<ActorId, Box<dyn std::any::Any + Send + Sync>>, // Type-erased actor handles
}

impl Actors {
    pub fn new() -> Self {
        Self {
            registry: HashMap::new(),
        }
    }

    /// Get an actor proxy for communication
    pub fn actor(&self, id: impl Into<ActorId>) -> ActorProxy {
        ActorProxy {
            id: id.into(),
            registry: self.clone(),
        }
    }

    /// Register an actor handle (internal use)
    pub fn register<T: Actor + 'static>(&mut self, id: ActorId, handle: ActorHandle<T>) {
        self.registry.insert(id, Box::new(handle));
    }
}

/// Proxy for actor communication - provides the actors.actor(id).act(action) interface
pub struct ActorProxy {
    id: ActorId,
    registry: Actors,
}

impl ActorProxy {
    /// Send an action to the actor - THE primary interaction pattern
    pub async fn act<A, R>(&self, action: A) -> TamtilResult<R>
    where
        A: Archive + for<'a> Serialize<rkyv::api::high::HighSerializer<rkyv::util::AlignedVec, rkyv::ser::allocator::ArenaHandle<'a>, RancorError>> + Send + Sync + 'static,
        R: Archive + for<'a> Serialize<rkyv::api::high::HighSerializer<rkyv::util::AlignedVec, rkyv::ser::allocator::ArenaHandle<'a>, RancorError>> + Send + Sync + 'static,
    {
        // For now, return an error - this will be implemented with proper type erasure
        Err(TamtilError::ActorNotFound { id: self.id.as_str().to_string() })
    }

    /// Get the actor ID
    pub fn id(&self) -> &ActorId {
        &self.id
    }
}

/// Core trait that all TAMTIL actors must implement
#[async_trait]
pub trait Actor: Send + Sync + 'static {
    type Action: Archive + for<'a> Serialize<rkyv::api::high::HighSerializer<rkyv::util::AlignedVec, rkyv::ser::allocator::ArenaHandle<'a>, RancorError>> + Send + Sync + 'static;
    type Reaction: Archive + for<'a> Serialize<rkyv::api::high::HighSerializer<rkyv::util::AlignedVec, rkyv::ser::allocator::ArenaHandle<'a>, RancorError>> + Send + Sync + 'static;

    /// Handle an action and return a reaction
    /// Actors can communicate with other actors via the actors parameter
    async fn act(&mut self, action: Self::Action, actors: &Actors) -> TamtilResult<Self::Reaction>;
}

// ============================================================================
// HANDLER PATTERN - Only exposes act() and new()
// ============================================================================

/// Handler provides a clean interface that only exposes act() and new()
/// This hides the complexity of the actor task/handle pattern
pub struct Handler<T: Actor> {
    handle: ActorHandle<T>,
}

impl<T: Actor> Handler<T> {
    /// Create a new handler by spawning an actor
    pub async fn new(id: impl Into<ActorId>, actor: T) -> TamtilResult<Self> {
        let mut platform = Platform::new();
        let handle = platform.spawn(id, actor).await?;
        Ok(Self { handle })
    }

    /// Send an action to the actor and get a reaction
    pub async fn act(&self, action: T::Action) -> TamtilResult<T::Reaction> {
        self.handle.act(action).await
    }
}

/// Message envelope for actor communication following Alice Ryhl's pattern
pub enum ActorMessage<A, R> {
    Action {
        action: A,
        respond_to: Option<oneshot::Sender<TamtilResult<R>>>,
    },
    Shutdown,
}

/// Actor task that runs independently following Alice Ryhl's pattern
pub struct ActorTask<T: Actor> {
    id: ActorId,
    actor: T,
    receiver: mpsc::Receiver<ActorMessage<T::Action, T::Reaction>>,
    actors: Actors,
}

impl<T: Actor> ActorTask<T> {
    pub fn new(
        id: ActorId,
        actor: T,
        receiver: mpsc::Receiver<ActorMessage<T::Action, T::Reaction>>,
        actors: Actors,
    ) -> Self {
        Self {
            id,
            actor,
            receiver,
            actors,
        }
    }

    /// Handle a single message
    async fn handle_message(&mut self, msg: ActorMessage<T::Action, T::Reaction>) -> TamtilResult<bool> {
        match msg {
            ActorMessage::Action { action, respond_to } => {
                let result = self.actor.act(action, &self.actors).await;

                if let Some(sender) = respond_to {
                    let _ = sender.send(result);
                }
                Ok(true) // Continue running
            }
            ActorMessage::Shutdown => {
                Ok(false) // Stop running
            }
        }
    }
}

/// Run the actor task (following Alice Ryhl's pattern)
pub async fn run_actor_task<T: Actor>(mut task: ActorTask<T>) -> TamtilResult<()> {
    tracing::info!("Actor {} starting", task.id.as_str());
    
    // Main message loop
    while let Some(msg) = task.receiver.recv().await {
        match task.handle_message(msg).await {
            Ok(true) => continue,  // Keep running
            Ok(false) => break,    // Shutdown requested
            Err(e) => {
                tracing::error!("Actor {} error: {}", task.id.as_str(), e);
                break;
            }
        }
    }
    
    tracing::info!("Actor {} stopped", task.id.as_str());
    Ok(())
}

/// Typed actor handle for type-safe communication
pub struct ActorHandle<T: Actor> {
    id: ActorId,
    sender: mpsc::Sender<ActorMessage<T::Action, T::Reaction>>,
}

impl<T: Actor> ActorHandle<T> {
    pub fn new(id: ActorId, sender: mpsc::Sender<ActorMessage<T::Action, T::Reaction>>) -> Self {
        Self { id, sender }
    }
    
    /// Send an action to the actor and wait for a reaction
    pub async fn act(&self, action: T::Action) -> TamtilResult<T::Reaction> {
        let (send, recv) = oneshot::channel();
        
        let msg = ActorMessage::Action {
            action,
            respond_to: Some(send),
        };
        
        self.sender.send(msg).await
            .map_err(|_| TamtilError::Communication {
                message: "Failed to send action to actor".to_string()
            })?;
            
        recv.await
            .map_err(|_| TamtilError::Communication {
                message: "Actor task has been killed".to_string()
            })?
    }
    
    /// Get the actor's ID
    pub fn id(&self) -> &ActorId {
        &self.id
    }
    
    /// Shutdown the actor
    pub async fn shutdown(&self) -> TamtilResult<()> {
        self.sender.send(ActorMessage::Shutdown).await
            .map_err(|_| TamtilError::Communication {
                message: "Failed to send shutdown message".to_string()
            })?;
        Ok(())
    }
}

// ============================================================================
// PLATFORM ACTOR - Spawns and manages context actors
// ============================================================================

/// Platform actor that manages context actors
pub struct PlatformActor {
    id: ActorId,
    contexts: HashMap<ActorId, ActorHandle<ContextActor>>,
}

impl PlatformActor {
    pub fn new(id: impl Into<ActorId>) -> Self {
        Self {
            id: id.into(),
            contexts: HashMap::new(),
        }
    }
}

#[async_trait]
impl Actor for PlatformActor {
    type Action = PlatformAction;
    type Reaction = PlatformReaction;

    async fn act(&mut self, action: Self::Action, actors: &Actors) -> TamtilResult<Self::Reaction> {
        match action {
            PlatformAction::Standard(StandardAction::Start) => {
                tracing::info!("Platform {} starting", self.id.as_str());
                Ok(PlatformReaction::Standard(StandardReaction::Started))
            }

            PlatformAction::Standard(StandardAction::Stop) => {
                tracing::info!("Platform {} stopping", self.id.as_str());
                // Stop all contexts using actors registry
                for (context_id, _) in &self.contexts {
                    let _ = actors.actor(context_id.clone())
                        .act::<ContextAction, ContextReaction>(ContextAction::Standard(StandardAction::Stop))
                        .await;
                }
                self.contexts.clear();
                Ok(PlatformReaction::Standard(StandardReaction::Stopped))
            }

            PlatformAction::Standard(StandardAction::Shutdown) => {
                tracing::info!("Platform {} shutting down", self.id.as_str());
                // Shutdown all contexts using actors registry
                for (context_id, context_handle) in &self.contexts {
                    let _ = actors.actor(context_id.clone())
                        .act::<ContextAction, ContextReaction>(ContextAction::Standard(StandardAction::Shutdown))
                        .await;
                    let _ = context_handle.shutdown().await;
                }
                self.contexts.clear();
                Ok(PlatformReaction::Standard(StandardReaction::Shutdown))
            }

            PlatformAction::StartContext { id } => {
                if self.contexts.contains_key(&id) {
                    return Ok(PlatformReaction::ContextStarted { id });
                }

                let context = ContextActor::new(id.clone());
                let mut platform = Platform::new();
                let handle = platform.spawn(id.clone(), context).await?;

                // Start the context using actors registry
                let _ = actors.actor(id.clone())
                    .act::<ContextAction, ContextReaction>(ContextAction::Standard(StandardAction::Start))
                    .await;

                self.contexts.insert(id.clone(), handle);
                tracing::info!("Platform {} started context {}", self.id.as_str(), id.as_str());
                Ok(PlatformReaction::ContextStarted { id })
            }

            PlatformAction::StopContext { id } => {
                if let Some(handle) = self.contexts.remove(&id) {
                    let _ = actors.actor(id.clone())
                        .act::<ContextAction, ContextReaction>(ContextAction::Standard(StandardAction::Stop))
                        .await;
                    let _ = handle.shutdown().await;
                    tracing::info!("Platform {} stopped context {}", self.id.as_str(), id.as_str());
                    Ok(PlatformReaction::ContextStopped { id })
                } else {
                    Ok(PlatformReaction::ContextNotFound { id })
                }
            }
        }
    }
}

// ============================================================================
// CONTEXT ACTOR - Spawns and manages regular actors
// ============================================================================

/// Context actor that manages regular actors
pub struct ContextActor {
    id: ActorId,
    actors: HashMap<ActorId, Box<dyn std::any::Any + Send + Sync>>, // Type-erased actor handles
}

impl ContextActor {
    pub fn new(id: impl Into<ActorId>) -> Self {
        Self {
            id: id.into(),
            actors: HashMap::new(),
        }
    }
}

#[async_trait]
impl Actor for ContextActor {
    type Action = ContextAction;
    type Reaction = ContextReaction;

    async fn act(&mut self, action: Self::Action, actors: &Actors) -> TamtilResult<Self::Reaction> {
        match action {
            ContextAction::Standard(StandardAction::Start) => {
                tracing::info!("Context {} starting", self.id.as_str());
                Ok(ContextReaction::Standard(StandardReaction::Started))
            }

            ContextAction::Standard(StandardAction::Stop) => {
                tracing::info!("Context {} stopping", self.id.as_str());
                // Stop all actors using actors registry
                for (actor_id, _) in &self.actors {
                    let _ = actors.actor(actor_id.clone())
                        .act::<CounterAction, CounterReaction>(CounterAction::Stop)
                        .await;
                }
                self.actors.clear();
                Ok(ContextReaction::Standard(StandardReaction::Stopped))
            }

            ContextAction::Standard(StandardAction::Shutdown) => {
                tracing::info!("Context {} shutting down", self.id.as_str());
                // Shutdown all actors using actors registry
                for (actor_id, _) in &self.actors {
                    let _ = actors.actor(actor_id.clone())
                        .act::<CounterAction, CounterReaction>(CounterAction::Stop)
                        .await;
                }
                self.actors.clear();
                Ok(ContextReaction::Standard(StandardReaction::Shutdown))
            }

            ContextAction::StartActor { id } => {
                // For this example, we'll create a counter actor
                // In a real implementation, this would be more flexible
                if self.actors.contains_key(&id) {
                    return Ok(ContextReaction::ActorStarted { id });
                }

                let counter = CounterActor::new(id.clone());
                let mut platform = Platform::new();
                let handle = platform.spawn(id.clone(), counter).await?;

                // Start the actor using actors registry
                let _ = actors.actor(id.clone())
                    .act::<CounterAction, CounterReaction>(CounterAction::Start)
                    .await;

                self.actors.insert(id.clone(), Box::new(handle));
                tracing::info!("Context {} started actor {}", self.id.as_str(), id.as_str());
                Ok(ContextReaction::ActorStarted { id })
            }

            ContextAction::StopActor { id } => {
                if self.actors.remove(&id).is_some() {
                    let _ = actors.actor(id.clone())
                        .act::<CounterAction, CounterReaction>(CounterAction::Stop)
                        .await;
                    tracing::info!("Context {} stopped actor {}", self.id.as_str(), id.as_str());
                    Ok(ContextReaction::ActorStopped { id })
                } else {
                    Ok(ContextReaction::ActorNotFound { id })
                }
            }
        }
    }
}

/// Platform manages the actor system and hides the underlying runtime
pub struct Platform {
    actors: Actors,
}

impl Platform {
    /// Create a new platform
    pub fn new() -> Self {
        Self {
            actors: Actors::new(),
        }
    }

    /// Spawn an actor on the platform
    pub async fn spawn<T: Actor + 'static>(
        &mut self,
        id: impl Into<ActorId>,
        actor: T,
    ) -> TamtilResult<ActorHandle<T>> {
        let actor_id = id.into();
        let (sender, receiver) = mpsc::channel(32);

        // Create the actor task with actors registry
        let task = ActorTask::new(
            actor_id.clone(),
            actor,
            receiver,
            self.actors.clone(),
        );

        // Spawn the task
        tokio::spawn(run_actor_task(task));

        // Create and return the handle
        let handle = ActorHandle::new(actor_id.clone(), sender);

        // Register the handle in the actors registry
        self.actors.register(actor_id, handle.clone());

        Ok(handle)
    }

    /// Get the actors registry for inter-actor communication
    pub fn actors(&self) -> &Actors {
        &self.actors
    }
}

impl Default for Platform {
    fn default() -> Self {
        Self::new()
    }
}

// ============================================================================
// EXAMPLE IMPLEMENTATION - Counter Actor
// ============================================================================

/// Example counter action using rkyv
#[derive(Debug, Clone, Archive, Serialize, Deserialize)]
#[rkyv(compare(PartialEq), derive(Debug))]
pub enum CounterAction {
    Start,
    Stop,
    Increment,
    Get,
}

/// Example counter reaction using rkyv
#[derive(Debug, Clone, Archive, Serialize, Deserialize)]
#[rkyv(compare(PartialEq), derive(Debug))]
pub enum CounterReaction {
    Started,
    Stopped,
    Incremented { new_count: u32 },
    Count { value: u32 },
}

/// Example counter actor implementation
pub struct CounterActor {
    id: ActorId,
    count: u32,
}

impl CounterActor {
    pub fn new(id: impl Into<ActorId>) -> Self {
        Self {
            id: id.into(),
            count: 0,
        }
    }
}

#[async_trait]
impl Actor for CounterActor {
    type Action = CounterAction;
    type Reaction = CounterReaction;

    async fn act(&mut self, action: Self::Action) -> TamtilResult<Self::Reaction> {
        match action {
            CounterAction::Start => {
                tracing::info!("Counter actor {} starting", self.id.as_str());
                Ok(CounterReaction::Started)
            }

            CounterAction::Stop => {
                tracing::info!("Counter actor {} stopping", self.id.as_str());
                Ok(CounterReaction::Stopped)
            }

            CounterAction::Increment => {
                self.count += 1;
                tracing::debug!("Counter actor {} incremented to {}", self.id.as_str(), self.count);
                Ok(CounterReaction::Incremented { new_count: self.count })
            }

            CounterAction::Get => {
                Ok(CounterReaction::Count { value: self.count })
            }
        }
    }
}

// ============================================================================
// EXAMPLE MODULE - Demonstrates platform->context->actor hierarchy
// ============================================================================

pub mod example {
    use super::*;

    /// Example demonstrating the full hierarchy: Platform -> Context -> Actor
    pub async fn run_hierarchy_example() -> TamtilResult<()> {
        // Initialize tracing
        let _ = tracing_subscriber::fmt::try_init();

        println!("🚀 Starting TAMTIL hierarchy example...");

        // 1. Create and start platform actor
        let platform_actor = PlatformActor::new("main-platform");
        let platform_handler = Handler::new("main-platform", platform_actor).await?;

        // Start the platform
        let reaction = platform_handler.act(PlatformAction::Standard(StandardAction::Start)).await?;
        println!("✅ Platform started: {:?}", reaction);

        // 2. Start a context through the platform
        let context_id = ActorId::new("web-context");
        let reaction = platform_handler.act(PlatformAction::StartContext {
            id: context_id.clone()
        }).await?;
        println!("✅ Context started: {:?}", reaction);

        // 3. For demonstration, we'll also create a direct context handler
        // In practice, you'd get this through the platform
        let context_actor = ContextActor::new("web-context");
        let context_handler = Handler::new("web-context", context_actor).await?;

        // Start the context
        let reaction = context_handler.act(ContextAction::Standard(StandardAction::Start)).await?;
        println!("✅ Context directly started: {:?}", reaction);

        // 4. Start an actor through the context
        let actor_id = ActorId::new("user-counter");
        let reaction = context_handler.act(ContextAction::StartActor {
            id: actor_id.clone()
        }).await?;
        println!("✅ Actor started: {:?}", reaction);

        // 5. For demonstration, create a direct counter handler
        let counter_actor = CounterActor::new("user-counter");
        let counter_handler = Handler::new("user-counter", counter_actor).await?;

        // Test the counter
        let reaction = counter_handler.act(CounterAction::Start).await?;
        println!("✅ Counter started: {:?}", reaction);

        let reaction = counter_handler.act(CounterAction::Increment).await?;
        println!("✅ Counter incremented: {:?}", reaction);

        let reaction = counter_handler.act(CounterAction::Get).await?;
        println!("✅ Counter value: {:?}", reaction);

        // 6. Stop everything in reverse order
        let reaction = counter_handler.act(CounterAction::Stop).await?;
        println!("✅ Counter stopped: {:?}", reaction);

        let reaction = context_handler.act(ContextAction::StopActor {
            id: actor_id
        }).await?;
        println!("✅ Actor stopped: {:?}", reaction);

        let reaction = context_handler.act(ContextAction::Standard(StandardAction::Stop)).await?;
        println!("✅ Context stopped: {:?}", reaction);

        let reaction = platform_handler.act(PlatformAction::StopContext {
            id: context_id
        }).await?;
        println!("✅ Context stopped via platform: {:?}", reaction);

        let reaction = platform_handler.act(PlatformAction::Standard(StandardAction::Stop)).await?;
        println!("✅ Platform stopped: {:?}", reaction);

        println!("🎉 TAMTIL hierarchy example completed successfully!");

        Ok(())
    }

    /// Example showing the Handler pattern with only act() and new()
    pub async fn run_handler_example() -> TamtilResult<()> {
        println!("🔧 Testing Handler pattern...");

        // Create a counter using only Handler::new() and Handler::act()
        let counter = CounterActor::new("handler-test");
        let handler = Handler::new("handler-test", counter).await?;

        // Use only act() method
        let reaction = handler.act(CounterAction::Start).await?;
        println!("Handler start: {:?}", reaction);

        let reaction = handler.act(CounterAction::Increment).await?;
        println!("Handler increment: {:?}", reaction);

        let reaction = handler.act(CounterAction::Get).await?;
        println!("Handler get: {:?}", reaction);

        let reaction = handler.act(CounterAction::Stop).await?;
        println!("Handler stop: {:?}", reaction);

        println!("✅ Handler pattern works perfectly!");

        Ok(())
    }
}

// ============================================================================
// TESTS
// ============================================================================

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_counter_actor() {
        // Initialize tracing for test output
        let _ = tracing_subscriber::fmt::try_init();

        // Create platform
        let platform = Platform::new();

        // Spawn counter actor
        let counter = CounterActor::new("test-counter");
        let handle = platform.spawn("test-counter", counter).await.unwrap();

        // Test start
        let reaction = handle.act(CounterAction::Start).await.unwrap();
        assert!(matches!(reaction, CounterReaction::Started));

        // Test increment
        let reaction = handle.act(CounterAction::Increment).await.unwrap();
        assert!(matches!(reaction, CounterReaction::Incremented { new_count: 1 }));

        // Test get
        let reaction = handle.act(CounterAction::Get).await.unwrap();
        assert!(matches!(reaction, CounterReaction::Count { value: 1 }));

        // Test another increment
        let reaction = handle.act(CounterAction::Increment).await.unwrap();
        assert!(matches!(reaction, CounterReaction::Incremented { new_count: 2 }));

        // Test get again
        let reaction = handle.act(CounterAction::Get).await.unwrap();
        assert!(matches!(reaction, CounterReaction::Count { value: 2 }));

        // Test stop
        let reaction = handle.act(CounterAction::Stop).await.unwrap();
        assert!(matches!(reaction, CounterReaction::Stopped));

        println!("✅ All tests passed! TAMTIL MVP is working with rkyv serialization.");
    }

    #[tokio::test]
    async fn test_rkyv_serialization() {
        // Test that our actions and reactions can be serialized with rkyv
        let action = CounterAction::Increment;
        let bytes = rkyv::to_bytes::<RancorError>(&action).unwrap();
        let archived = rkyv::access::<rkyv::Archived<CounterAction>, RancorError>(&bytes).unwrap();
        assert_eq!(archived, &action);

        let reaction = CounterReaction::Incremented { new_count: 42 };
        let bytes = rkyv::to_bytes::<RancorError>(&reaction).unwrap();
        let archived = rkyv::access::<rkyv::Archived<CounterReaction>, RancorError>(&bytes).unwrap();
        assert_eq!(archived, &reaction);

        println!("✅ rkyv serialization working correctly!");
    }
}
