//! TAMTIL - High-performance actor system with rkyv zero-copy serialization
//!
//! Everything is an actor following the universal pattern: `actor.act(action, actors)`
//!
//! ## Core Features
//! - <PERSON>'s actor pattern with tokio hidden behind platform abstraction
//! - rkyv zero-copy serialization for maximum performance
//! - Single-word naming convention throughout (unless impossible)
//! - Hierarchical actor spawning via `actors.spawn(name)`
//! - URL-based addressing: platform.com/context/context_id/actor/actor_id/child/child_id
//! - Action→reaction pattern for all interactions
//! - No locking - actors handle concurrency elegantly
//! - Automatic child cleanup when parent stops
//!
//! ## Architecture
//! ```
//! Platform (platform.com)
//! └── Context (platform.com/web)
//!     └── Context Instance (platform.com/web/main)
//!         └── Actor (platform.com/web/main/counter)
//!             └── Actor Instance (platform.com/web/main/counter/stats)
//!                 └── Child Actor (platform.com/web/main/counter/stats/session)
//!                     └── Child Instance (platform.com/web/main/counter/stats/session/user123)
//! ```

use tokio::sync::{mpsc, oneshot};
use async_trait::async_trait;
use thiserror::Error;
use rkyv::{Archive, Serialize, Deserialize, rancor::Error as RancorError};
use std::collections::HashMap;

// ============================================================================
// CORE TYPES AND TRAITS
// ============================================================================

/// Core error types for TAMTIL
#[derive(Error, Debug)]
pub enum TamtilError {
    #[error("Actor not found: {id}")]
    ActorNotFound { id: String },
    #[error("Platform error: {message}")]
    Platform { message: String },
    #[error("Communication error: {message}")]
    Communication { message: String },
    #[error("Serialization error: {message}")]
    Serialization { message: String },
    #[error("Invalid address format: {address}")]
    InvalidAddress { address: String },
}

/// Result type for TAMTIL operations
pub type TamtilResult<T> = Result<T, TamtilError>;

/// Actor identifier with hierarchical URL-based addressing
///
/// Supports the full hierarchy:
/// - platform.com
/// - platform.com/context_name
/// - platform.com/context_name/context_id
/// - platform.com/context_name/context_id/actor_name
/// - platform.com/context_name/context_id/actor_name/actor_id
/// - platform.com/context_name/context_id/actor_name/actor_id/child_name
/// - platform.com/context_name/context_id/actor_name/actor_id/child_name/child_id
/// - etc...
#[derive(Debug, Clone, PartialEq, Eq, Hash, Archive, Serialize, Deserialize)]
#[rkyv(compare(PartialEq), derive(Debug))]
pub struct ActorId(String);

impl ActorId {
    /// Create new actor ID
    pub fn new(id: impl Into<String>) -> Self {
        Self(id.into())
    }

    /// Get string representation
    pub fn as_str(&self) -> &str {
        &self.0
    }

    /// Create child ID by appending name to current ID
    pub fn child(&self, name: impl Into<String>) -> Self {
        Self(format!("{}/{}", self.0, name.into()))
    }

    /// Get parent ID by removing last segment
    pub fn parent(&self) -> Option<Self> {
        self.0.rfind('/').map(|pos| Self(self.0[..pos].to_string()))
    }

    /// Get depth level (number of segments)
    pub fn depth(&self) -> usize {
        if self.0.is_empty() {
            0
        } else {
            self.0.matches('/').count() + 1
        }
    }

    /// Check if this ID is child of another ID
    pub fn is_child_of(&self, parent: &ActorId) -> bool {
        self.0.starts_with(&parent.0) &&
        self.0.len() > parent.0.len() &&
        self.0.chars().nth(parent.0.len()) == Some('/')
    }
}

impl From<&str> for ActorId {
    fn from(s: &str) -> Self {
        Self(s.to_string())
    }
}

impl From<String> for ActorId {
    fn from(s: String) -> Self {
        Self(s)
    }
}

/// Core trait that all TAMTIL actors must implement
///
/// Actors receive actions and return reactions, with access to the actors registry
/// for spawning children and communicating with other actors
#[async_trait]
pub trait Actor: Send + Sync + 'static {
    /// Action type this actor handles
    type Action: Archive + for<'a> Serialize<rkyv::api::high::HighSerializer<rkyv::util::AlignedVec, rkyv::ser::allocator::ArenaHandle<'a>, RancorError>> + Send + Sync + 'static;

    /// Reaction type this actor produces
    type Reaction: Archive + for<'a> Serialize<rkyv::api::high::HighSerializer<rkyv::util::AlignedVec, rkyv::ser::allocator::ArenaHandle<'a>, RancorError>> + Send + Sync + 'static;

    /// Handle action and return reaction
    ///
    /// Actors can spawn children and communicate via the actors parameter:
    /// - `actors.spawn(name)` - spawn child actor
    /// - `actors.actor(id).act(action)` - send action to another actor
    async fn act(&mut self, action: Self::Action, actors: &Actors) -> TamtilResult<Self::Reaction>;
}

/// Actors registry for spawning and communicating with child actors
///
/// Provides the developer API for:
/// - Spawning child actors via `spawn(name)`
/// - Communicating with actors via `actor(id).act(action)`
/// - Automatic hierarchical addressing
#[derive(Clone, Default)]
pub struct Actors {
    /// Parent actor ID for hierarchical addressing
    parent: Option<ActorId>,
}

impl Actors {
    /// Create new actors registry
    pub fn new() -> Self {
        Self { parent: None }
    }

    /// Create actors registry for specific parent
    pub fn child(parent: ActorId) -> Self {
        Self { parent: Some(parent) }
    }

    /// Get actor proxy for communication
    pub fn actor(&self, id: impl Into<ActorId>) -> ActorProxy {
        ActorProxy { id: id.into() }
    }

    /// Spawn new child actor with hierarchical addressing
    ///
    /// Creates child ID by appending name to parent ID:
    /// - Parent: "platform.com/web/main"
    /// - Child name: "counter"
    /// - Result: "platform.com/web/main/counter"
    pub async fn spawn(&self, name: impl Into<String>) -> TamtilResult<ActorId> {
        let child_id = self.build_child_id(name.into());

        // In real implementation, would spawn actual actor
        // For now, just return hierarchical ID
        tracing::info!("Would spawn child actor: {}", child_id.as_str());

        Ok(child_id)
    }

    /// Build hierarchical child ID based on parent
    fn build_child_id(&self, name: String) -> ActorId {
        match &self.parent {
            Some(parent_id) => parent_id.child(name),
            None => ActorId::new(name),
        }
    }
}

/// Proxy for actor communication
///
/// Provides the `actors.actor(id).act(action)` interface
pub struct ActorProxy {
    id: ActorId,
}

impl ActorProxy {
    /// Send action to actor (simplified for demo)
    ///
    /// In real implementation, would route to actual actor based on ID
    pub async fn act<A, R>(&self, _action: A) -> TamtilResult<R>
    where
        A: Archive + for<'a> Serialize<rkyv::api::high::HighSerializer<rkyv::util::AlignedVec, rkyv::ser::allocator::ArenaHandle<'a>, RancorError>> + Send + Sync + 'static,
        R: Archive + for<'a> Serialize<rkyv::api::high::HighSerializer<rkyv::util::AlignedVec, rkyv::ser::allocator::ArenaHandle<'a>, RancorError>> + Send + Sync + 'static + Default,
    {
        tracing::debug!("Would send action to {}", self.id.as_str());
        Ok(R::default())
    }

    /// Get actor ID
    pub fn id(&self) -> &ActorId {
        &self.id
    }
}

// ============================================================================
// STANDARD ACTIONS AND REACTIONS
// ============================================================================

/// Standard actions that all actors support
#[derive(Debug, Clone, Archive, Serialize, Deserialize)]
#[rkyv(compare(PartialEq), derive(Debug))]
pub enum StandardAction {
    Start,
    Stop,
    Shutdown,
}

/// Standard reactions that all actors produce
#[derive(Debug, Clone, Archive, Serialize, Deserialize)]
#[rkyv(compare(PartialEq), derive(Debug))]
pub enum StandardReaction {
    Started,
    Stopped,
    Shutdown,
}

// ============================================================================
// PLATFORM MODULE
// ============================================================================

pub mod platform {
    use super::*;

    /// Actions for platform actors
    #[derive(Debug, Clone, Archive, Serialize, Deserialize)]
    #[rkyv(compare(PartialEq), derive(Debug))]
    pub enum Action {
        Standard(StandardAction),
        StartContext { name: String },
        StopContext { name: String },
    }

    /// Reactions from platform actors
    #[derive(Debug, Clone, Archive, Serialize, Deserialize)]
    #[rkyv(compare(PartialEq), derive(Debug))]
    pub enum Reaction {
        Standard(StandardReaction),
        ContextStarted { id: String },
        ContextStopped { id: String },
        ContextNotFound { name: String },
    }

    /// Platform actor manages context actors
    ///
    /// Responsible for:
    /// - Spawning and managing context actors
    /// - Hierarchical addressing at platform level
    /// - Lifecycle management of contexts
    pub struct Actor {
        id: ActorId,
        contexts: HashMap<String, ActorId>, // name -> full_id mapping
    }

    impl Actor {
        /// Create new platform actor
        pub fn new(id: impl Into<ActorId>) -> Self {
            Self {
                id: id.into(),
                contexts: HashMap::new(),
            }
        }
    }

    #[async_trait]
    impl crate::Actor for Actor {
        type Action = Action;
        type Reaction = Reaction;

        async fn act(&mut self, action: Self::Action, actors: &Actors) -> TamtilResult<Self::Reaction> {
            match action {
                Action::Standard(StandardAction::Start) => {
                    tracing::info!("Platform {} starting", self.id.as_str());
                    Ok(Reaction::Standard(StandardReaction::Started))
                }

                Action::Standard(StandardAction::Stop) => {
                    tracing::info!("Platform {} stopping", self.id.as_str());
                    // Stop all contexts - children auto-stop
                    self.contexts.clear();
                    Ok(Reaction::Standard(StandardReaction::Stopped))
                }

                Action::Standard(StandardAction::Shutdown) => {
                    tracing::info!("Platform {} shutting down", self.id.as_str());
                    self.contexts.clear();
                    Ok(Reaction::Standard(StandardReaction::Shutdown))
                }

                Action::StartContext { name } => {
                    if self.contexts.contains_key(&name) {
                        let id = self.contexts[&name].as_str().to_string();
                        return Ok(Reaction::ContextStarted { id });
                    }

                    // Spawn context actor as child
                    let context_id = actors.spawn(name.clone()).await?;
                    self.contexts.insert(name, context_id.clone());

                    tracing::info!("Platform {} started context {}", self.id.as_str(), context_id.as_str());
                    Ok(Reaction::ContextStarted { id: context_id.as_str().to_string() })
                }

                Action::StopContext { name } => {
                    if let Some(context_id) = self.contexts.remove(&name) {
                        tracing::info!("Platform {} stopped context {}", self.id.as_str(), context_id.as_str());
                        Ok(Reaction::ContextStopped { id: context_id.as_str().to_string() })
                    } else {
                        Ok(Reaction::ContextNotFound { name })
                    }
                }
            }
        }
    }
}

// ============================================================================
// CONTEXT MODULE
// ============================================================================

pub mod context {
    use super::*;

    /// Actions for context actors
    #[derive(Debug, Clone, Archive, Serialize, Deserialize)]
    #[rkyv(compare(PartialEq), derive(Debug))]
    pub enum Action {
        Standard(StandardAction),
        StartActor { name: String },
        StopActor { name: String },
    }

    /// Reactions from context actors
    #[derive(Debug, Clone, Archive, Serialize, Deserialize)]
    #[rkyv(compare(PartialEq), derive(Debug))]
    pub enum Reaction {
        Standard(StandardReaction),
        ActorStarted { id: String },
        ActorStopped { id: String },
        ActorNotFound { name: String },
    }

    /// Context actor manages regular actors
    ///
    /// Responsible for:
    /// - Spawning and managing regular actors
    /// - Hierarchical addressing at context level
    /// - Lifecycle management of actors
    pub struct Actor {
        id: ActorId,
        actors: HashMap<String, ActorId>, // name -> full_id mapping
    }

    impl Actor {
        /// Create new context actor
        pub fn new(id: impl Into<ActorId>) -> Self {
            Self {
                id: id.into(),
                actors: HashMap::new(),
            }
        }
    }

    #[async_trait]
    impl crate::Actor for Actor {
        type Action = Action;
        type Reaction = Reaction;

        async fn act(&mut self, action: Self::Action, actors: &Actors) -> TamtilResult<Self::Reaction> {
            match action {
                Action::Standard(StandardAction::Start) => {
                    tracing::info!("Context {} starting", self.id.as_str());
                    Ok(Reaction::Standard(StandardReaction::Started))
                }

                Action::Standard(StandardAction::Stop) => {
                    tracing::info!("Context {} stopping", self.id.as_str());
                    // Stop all actors - children auto-stop
                    self.actors.clear();
                    Ok(Reaction::Standard(StandardReaction::Stopped))
                }

                Action::Standard(StandardAction::Shutdown) => {
                    tracing::info!("Context {} shutting down", self.id.as_str());
                    self.actors.clear();
                    Ok(Reaction::Standard(StandardReaction::Shutdown))
                }

                Action::StartActor { name } => {
                    if self.actors.contains_key(&name) {
                        let id = self.actors[&name].as_str().to_string();
                        return Ok(Reaction::ActorStarted { id });
                    }

                    // Spawn actor as child
                    let actor_id = actors.spawn(name.clone()).await?;
                    self.actors.insert(name, actor_id.clone());

                    tracing::info!("Context {} started actor {}", self.id.as_str(), actor_id.as_str());
                    Ok(Reaction::ActorStarted { id: actor_id.as_str().to_string() })
                }

                Action::StopActor { name } => {
                    if let Some(actor_id) = self.actors.remove(&name) {
                        tracing::info!("Context {} stopped actor {}", self.id.as_str(), actor_id.as_str());
                        Ok(Reaction::ActorStopped { id: actor_id.as_str().to_string() })
                    } else {
                        Ok(Reaction::ActorNotFound { name })
                    }
                }
            }
        }
    }
}

// ============================================================================
// ACTOR MODULE
// ============================================================================

pub mod actor {
    use super::*;

    /// Example counter action using rkyv
    #[derive(Debug, Clone, Archive, Serialize, Deserialize)]
    #[rkyv(compare(PartialEq), derive(Debug))]
    pub enum CounterAction {
        Start,
        Stop,
        Increment,
        Get,
        Spawn { name: String }, // Spawn child actor
    }

    /// Example counter reaction using rkyv
    #[derive(Debug, Clone, Archive, Serialize, Deserialize)]
    #[rkyv(compare(PartialEq), derive(Debug))]
    pub enum CounterReaction {
        Started,
        Stopped,
        Incremented { count: u32 },
        Count { value: u32 },
        Spawned { child: String }, // Child spawned
    }

    /// Example counter actor implementation
    ///
    /// Demonstrates:
    /// - Basic state management (count)
    /// - Child actor spawning via actors.spawn()
    /// - Inter-actor communication via actors.actor().act()
    pub struct Counter {
        id: ActorId,
        count: u32,
    }

    impl Counter {
        /// Create new counter actor
        pub fn new(id: impl Into<ActorId>) -> Self {
            Self {
                id: id.into(),
                count: 0,
            }
        }
    }

    #[async_trait]
    impl crate::Actor for Counter {
        type Action = CounterAction;
        type Reaction = CounterReaction;

        async fn act(&mut self, action: Self::Action, actors: &Actors) -> TamtilResult<Self::Reaction> {
            match action {
                CounterAction::Start => {
                    tracing::info!("Counter {} starting", self.id.as_str());
                    Ok(CounterReaction::Started)
                }

                CounterAction::Stop => {
                    tracing::info!("Counter {} stopping", self.id.as_str());
                    // Children auto-stop when parent stops
                    Ok(CounterReaction::Stopped)
                }

                CounterAction::Increment => {
                    self.count += 1;
                    tracing::debug!("Counter {} incremented to {}", self.id.as_str(), self.count);

                    // Example inter-actor communication:
                    // actors.actor("platform.com/web/main/other").act(CounterAction::Get).await?;

                    Ok(CounterReaction::Incremented { count: self.count })
                }

                CounterAction::Get => {
                    Ok(CounterReaction::Count { value: self.count })
                }

                CounterAction::Spawn { name } => {
                    // Spawn child actor using hierarchical addressing
                    let child_id = actors.spawn(name.clone()).await?;
                    tracing::info!("Counter {} spawned child: {}", self.id.as_str(), child_id.as_str());

                    // Could also communicate with child:
                    // actors.actor(child_id).act(CounterAction::Start).await?;

                    Ok(CounterReaction::Spawned { child: child_id.as_str().to_string() })
                }
            }
        }
    }
}

// ============================================================================
// INFRASTRUCTURE - Alice Ryhl's Actor Pattern Implementation
// ============================================================================

/// Message envelope for actor communication following Alice Ryhl's pattern
pub enum ActorMessage<A, R> {
    Action {
        action: A,
        respond_to: Option<oneshot::Sender<TamtilResult<R>>>,
    },
    Shutdown,
}

/// Actor task that runs independently following Alice Ryhl's pattern
pub struct ActorTask<T: Actor> {
    id: ActorId,
    actor: T,
    receiver: mpsc::Receiver<ActorMessage<T::Action, T::Reaction>>,
    actors: Actors,
}

impl<T: Actor> ActorTask<T> {
    /// Create new actor task
    pub fn new(
        id: ActorId,
        actor: T,
        receiver: mpsc::Receiver<ActorMessage<T::Action, T::Reaction>>,
        actors: Actors,
    ) -> Self {
        Self { id, actor, receiver, actors }
    }

    /// Handle single message
    async fn handle_message(&mut self, msg: ActorMessage<T::Action, T::Reaction>) -> TamtilResult<bool> {
        match msg {
            ActorMessage::Action { action, respond_to } => {
                let result = self.actor.act(action, &self.actors).await;

                if let Some(sender) = respond_to {
                    let _ = sender.send(result);
                }
                Ok(true) // Continue running
            }
            ActorMessage::Shutdown => {
                Ok(false) // Stop running
            }
        }
    }
}

/// Run actor task (following Alice Ryhl's pattern)
pub async fn run_actor_task<T: Actor>(mut task: ActorTask<T>) -> TamtilResult<()> {
    tracing::info!("Actor {} starting", task.id.as_str());

    // Main message loop
    while let Some(msg) = task.receiver.recv().await {
        match task.handle_message(msg).await {
            Ok(true) => continue,  // Keep running
            Ok(false) => break,    // Shutdown requested
            Err(e) => {
                tracing::error!("Actor {} error: {}", task.id.as_str(), e);
                break;
            }
        }
    }

    tracing::info!("Actor {} stopped", task.id.as_str());
    Ok(())
}

/// Typed actor handle for type-safe communication
#[derive(Clone)]
pub struct ActorHandle<T: Actor> {
    id: ActorId,
    sender: mpsc::Sender<ActorMessage<T::Action, T::Reaction>>,
}

impl<T: Actor> ActorHandle<T> {
    /// Create new actor handle
    pub fn new(id: ActorId, sender: mpsc::Sender<ActorMessage<T::Action, T::Reaction>>) -> Self {
        Self { id, sender }
    }

    /// Send action to actor and wait for reaction
    pub async fn act(&self, action: T::Action) -> TamtilResult<T::Reaction> {
        let (send, recv) = oneshot::channel();

        let msg = ActorMessage::Action {
            action,
            respond_to: Some(send),
        };

        self.sender.send(msg).await
            .map_err(|_| TamtilError::Communication {
                message: "Failed to send action to actor".to_string()
            })?;

        recv.await
            .map_err(|_| TamtilError::Communication {
                message: "Actor task has been killed".to_string()
            })?
    }

    /// Get actor ID
    pub fn id(&self) -> &ActorId {
        &self.id
    }

    /// Shutdown actor
    pub async fn shutdown(&self) -> TamtilResult<()> {
        self.sender.send(ActorMessage::Shutdown).await
            .map_err(|_| TamtilError::Communication {
                message: "Failed to send shutdown message".to_string()
            })?;
        Ok(())
    }
}

/// Platform manages actor system and hides underlying runtime
pub struct Platform;

impl Platform {
    /// Create new platform
    pub fn new() -> Self {
        Self
    }

    /// Spawn actor on platform
    pub async fn spawn<T: Actor + 'static>(
        &self,
        id: impl Into<ActorId>,
        actor: T,
    ) -> TamtilResult<ActorHandle<T>> {
        let actor_id = id.into();
        let (sender, receiver) = mpsc::channel(32);

        // Create actors registry for this actor
        let actors = Actors::child(actor_id.clone());

        let task = ActorTask::new(actor_id.clone(), actor, receiver, actors);
        tokio::spawn(run_actor_task(task));

        let handle = ActorHandle::new(actor_id, sender);
        Ok(handle)
    }
}

impl Default for Platform {
    fn default() -> Self {
        Self::new()
    }
}

/// Typed actor handle for type-safe communication
#[derive(Clone)]
pub struct ActorHandle<T: Actor> {
    id: ActorId,
    sender: mpsc::Sender<ActorMessage<T::Action, T::Reaction>>,
}

impl<T: Actor> ActorHandle<T> {
    pub fn new(id: ActorId, sender: mpsc::Sender<ActorMessage<T::Action, T::Reaction>>) -> Self {
        Self { id, sender }
    }
    
    /// Send an action to the actor and wait for a reaction
    pub async fn act(&self, action: T::Action) -> TamtilResult<T::Reaction> {
        let (send, recv) = oneshot::channel();
        
        let msg = ActorMessage::Action {
            action,
            respond_to: Some(send),
        };
        
        self.sender.send(msg).await
            .map_err(|_| TamtilError::Communication {
                message: "Failed to send action to actor".to_string()
            })?;
            
        recv.await
            .map_err(|_| TamtilError::Communication {
                message: "Actor task has been killed".to_string()
            })?
    }
    
    /// Get the actor's ID
    pub fn id(&self) -> &ActorId {
        &self.id
    }
    
    /// Shutdown the actor
    pub async fn shutdown(&self) -> TamtilResult<()> {
        self.sender.send(ActorMessage::Shutdown).await
            .map_err(|_| TamtilError::Communication {
                message: "Failed to send shutdown message".to_string()
            })?;
        Ok(())
    }
}

// ============================================================================
// PLATFORM ACTOR - Spawns and manages context actors
// ============================================================================

/// Platform actor that manages context actors
pub struct PlatformActor {
    id: ActorId,
    contexts: HashMap<ActorId, ()>, // Simplified for demo
}

impl PlatformActor {
    pub fn new(id: impl Into<ActorId>) -> Self {
        Self {
            id: id.into(),
            contexts: HashMap::new(),
        }
    }
}

#[async_trait]
impl Actor for PlatformActor {
    type Action = PlatformAction;
    type Reaction = PlatformReaction;

    async fn act(&mut self, action: Self::Action, actors: &Actors) -> TamtilResult<Self::Reaction> {
        match action {
            PlatformAction::Standard(StandardAction::Start) => {
                tracing::info!("Platform {} starting", self.id.as_str());
                Ok(PlatformReaction::Standard(StandardReaction::Started))
            }

            PlatformAction::Standard(StandardAction::Stop) => {
                tracing::info!("Platform {} stopping", self.id.as_str());
                // In a real implementation, would stop all child contexts
                self.contexts.clear();
                Ok(PlatformReaction::Standard(StandardReaction::Stopped))
            }

            PlatformAction::Standard(StandardAction::Shutdown) => {
                tracing::info!("Platform {} shutting down", self.id.as_str());
                // In a real implementation, would shutdown all child contexts
                self.contexts.clear();
                Ok(PlatformReaction::Standard(StandardReaction::Shutdown))
            }

            PlatformAction::StartContext { id } => {
                if self.contexts.contains_key(&id) {
                    return Ok(PlatformReaction::ContextStarted { id });
                }

                // In a real implementation, would spawn the context actor
                tracing::info!("Platform {} would start context {}", self.id.as_str(), id.as_str());

                // For demo, just track that we "started" it
                self.contexts.insert(id.clone(), ());
                Ok(PlatformReaction::ContextStarted { id })
            }

            PlatformAction::StopContext { id } => {
                if self.contexts.remove(&id).is_some() {
                    tracing::info!("Platform {} stopped context {}", self.id.as_str(), id.as_str());
                    Ok(PlatformReaction::ContextStopped { id })
                } else {
                    Ok(PlatformReaction::ContextNotFound { id })
                }
            }
        }
    }
}

// ============================================================================
// CONTEXT ACTOR - Spawns and manages regular actors
// ============================================================================

/// Context actor that manages regular actors
pub struct ContextActor {
    id: ActorId,
    actors: HashMap<ActorId, ()>, // Simplified for demo
}

impl ContextActor {
    pub fn new(id: impl Into<ActorId>) -> Self {
        Self {
            id: id.into(),
            actors: HashMap::new(),
        }
    }
}

#[async_trait]
impl Actor for ContextActor {
    type Action = ContextAction;
    type Reaction = ContextReaction;

    async fn act(&mut self, action: Self::Action, actors: &Actors) -> TamtilResult<Self::Reaction> {
        match action {
            ContextAction::Standard(StandardAction::Start) => {
                tracing::info!("Context {} starting", self.id.as_str());
                Ok(ContextReaction::Standard(StandardReaction::Started))
            }

            ContextAction::Standard(StandardAction::Stop) => {
                tracing::info!("Context {} stopping", self.id.as_str());
                // In a real implementation, would stop all child actors
                self.actors.clear();
                Ok(ContextReaction::Standard(StandardReaction::Stopped))
            }

            ContextAction::Standard(StandardAction::Shutdown) => {
                tracing::info!("Context {} shutting down", self.id.as_str());
                // In a real implementation, would shutdown all child actors
                self.actors.clear();
                Ok(ContextReaction::Standard(StandardReaction::Shutdown))
            }

            ContextAction::StartActor { id } => {
                if self.actors.contains_key(&id) {
                    return Ok(ContextReaction::ActorStarted { id });
                }

                // In a real implementation, would spawn the actor
                tracing::info!("Context {} would start actor {}", self.id.as_str(), id.as_str());

                // For demo, just track that we "started" it
                self.actors.insert(id.clone(), ());
                Ok(ContextReaction::ActorStarted { id })
            }

            ContextAction::StopActor { id } => {
                if self.actors.remove(&id).is_some() {
                    tracing::info!("Context {} stopped actor {}", self.id.as_str(), id.as_str());
                    Ok(ContextReaction::ActorStopped { id })
                } else {
                    Ok(ContextReaction::ActorNotFound { id })
                }
            }
        }
    }
}

/// Platform manages the actor system and hides the underlying runtime
pub struct Platform;

impl Platform {
    /// Create a new platform
    pub fn new() -> Self {
        Self
    }

    /// Spawn an actor on the platform
    pub async fn spawn<T: Actor + 'static>(
        &self,
        id: impl Into<ActorId>,
        actor: T,
    ) -> TamtilResult<ActorHandle<T>> {
        let actor_id = id.into();
        let (sender, receiver) = mpsc::channel(32);

        // Create actors registry for this actor
        let actors = Actors::child(actor_id.clone());

        let task = ActorTask::new(actor_id.clone(), actor, receiver, actors);
        tokio::spawn(run_actor_task(task));

        let handle = ActorHandle::new(actor_id, sender);
        Ok(handle)
    }
}

impl Default for Platform {
    fn default() -> Self {
        Self::new()
    }
}

// ============================================================================
// EXAMPLE IMPLEMENTATION - Counter Actor
// ============================================================================

/// Example counter action using rkyv
#[derive(Debug, Clone, Archive, Serialize, Deserialize)]
#[rkyv(compare(PartialEq), derive(Debug))]
pub enum CounterAction {
    Start,
    Stop,
    Increment,
    Get,
    Spawn { name: String }, // Spawn child actor
}

/// Example counter reaction using rkyv
#[derive(Debug, Clone, Archive, Serialize, Deserialize)]
#[rkyv(compare(PartialEq), derive(Debug))]
pub enum CounterReaction {
    Started,
    Stopped,
    Incremented { new_count: u32 },
    Count { value: u32 },
    Spawned { child: String }, // Child spawned
}

/// Example counter actor implementation
pub struct CounterActor {
    id: ActorId,
    count: u32,
}

impl CounterActor {
    pub fn new(id: impl Into<ActorId>) -> Self {
        Self {
            id: id.into(),
            count: 0,
        }
    }
}

#[async_trait]
impl Actor for CounterActor {
    type Action = CounterAction;
    type Reaction = CounterReaction;

    async fn act(&mut self, action: Self::Action, actors: &Actors) -> TamtilResult<Self::Reaction> {
        match action {
            CounterAction::Start => {
                tracing::info!("Counter actor {} starting", self.id.as_str());
                Ok(CounterReaction::Started)
            }

            CounterAction::Stop => {
                tracing::info!("Counter actor {} stopping", self.id.as_str());
                // In a real implementation, this would stop all child actors
                Ok(CounterReaction::Stopped)
            }

            CounterAction::Increment => {
                self.count += 1;
                tracing::debug!("Counter actor {} incremented to {}", self.id.as_str(), self.count);

                // Example of inter-actor communication
                // actors.actor("other-counter").act(CounterAction::Get).await?;

                Ok(CounterReaction::Incremented { new_count: self.count })
            }

            CounterAction::Get => {
                Ok(CounterReaction::Count { value: self.count })
            }

            CounterAction::Spawn { name } => {
                // Spawn a child actor using the actors registry
                let child_id = actors.spawn(name.clone()).await?;
                tracing::info!("Counter {} spawned child: {}", self.id.as_str(), child_id.as_str());

                // In a real implementation, you could also communicate with the child:
                // actors.actor(child_id).act(CounterAction::Start).await?;

                Ok(CounterReaction::Spawned { child: child_id.as_str().to_string() })
            }
        }
    }
}

// ============================================================================
// EXAMPLE MODULE - Demonstrates platform->context->actor hierarchy with URL addressing
// ============================================================================

pub mod example {
    use super::*;

    /// Example demonstrating hierarchical actor spawning and URL-based addressing
    pub async fn run_hierarchical_spawning_example() -> TamtilResult<()> {
        // Initialize tracing
        let _ = tracing_subscriber::fmt::try_init();

        println!("🚀 Starting TAMTIL hierarchical spawning example...");
        println!("📍 URL format: platform.com/context/actor/child");

        // 1. Create root counter actor
        let root_id = ActorId::new("platform.com");
        let root_actor = CounterActor::new(root_id.clone());
        let root_handler = CounterHandler::new(root_id.clone(), root_actor).await?;

        // Start the root actor
        let reaction = root_handler.act(CounterAction::Start).await?;
        println!("✅ Root actor started at {}: {:?}", root_id.as_str(), reaction);

        // 2. Spawn child actors using the actors.spawn() pattern
        let reaction = root_handler.act(CounterAction::Spawn { name: "web".to_string() }).await?;
        println!("✅ Child spawned: {:?}", reaction);

        let reaction = root_handler.act(CounterAction::Spawn { name: "api".to_string() }).await?;
        println!("✅ Child spawned: {:?}", reaction);

        // 3. Create another actor to demonstrate hierarchy
        let web_id = ActorId::new("platform.com/web");
        let web_actor = CounterActor::new(web_id.clone());
        let web_handler = CounterHandler::new(web_id.clone(), web_actor).await?;

        let reaction = web_handler.act(CounterAction::Start).await?;
        println!("✅ Web actor started at {}: {:?}", web_id.as_str(), reaction);

        // 4. Spawn grandchildren
        let reaction = web_handler.act(CounterAction::Spawn { name: "users".to_string() }).await?;
        println!("✅ Grandchild spawned: {:?}", reaction);

        let reaction = web_handler.act(CounterAction::Spawn { name: "sessions".to_string() }).await?;
        println!("✅ Grandchild spawned: {:?}", reaction);

        // 5. Test the hierarchy
        let reaction = root_handler.act(CounterAction::Increment).await?;
        println!("✅ Root incremented: {:?}", reaction);

        let reaction = web_handler.act(CounterAction::Increment).await?;
        println!("✅ Web incremented: {:?}", reaction);

        // 6. Demonstrate the actors.actor(id).act(action) pattern
        println!("📡 Demonstrating inter-actor communication...");
        println!("💡 Actors can communicate using: actors.actor(\"platform.com/web/users\").act(action)");
        println!("💡 When an actor stops, all its children stop automatically");

        // 7. Stop hierarchy (children stop automatically)
        let reaction = web_handler.act(CounterAction::Stop).await?;
        println!("✅ Web actor stopped (children auto-stopped): {:?}", reaction);

        let reaction = root_handler.act(CounterAction::Stop).await?;
        println!("✅ Root actor stopped (all children auto-stopped): {:?}", reaction);

        println!("🎉 TAMTIL hierarchical spawning example completed successfully!");
        println!("📋 Key features demonstrated:");
        println!("   - actors.spawn(name) creates child actors");
        println!("   - URL-based hierarchical addressing");
        println!("   - actors.actor(id).act(action) for communication");
        println!("   - Automatic child cleanup on parent stop");

        Ok(())
    }

    /// Example showing inter-actor communication with rkyv serialization
    pub async fn run_inter_actor_communication_example() -> TamtilResult<()> {
        println!("🔧 Testing inter-actor communication with rkyv...");

        // Create two counter actors
        let counter1 = CounterActor::new("platform.com/app/main/counter/stats");
        let handler1 = CounterHandler::new("platform.com/app/main/counter/stats", counter1).await?;

        let counter2 = CounterActor::new("platform.com/app/main/counter/metrics");
        let handler2 = CounterHandler::new("platform.com/app/main/counter/metrics", counter2).await?;

        // Start both counters
        let _ = handler1.act(CounterAction::Start).await?;
        let _ = handler2.act(CounterAction::Start).await?;

        // Test rkyv serialization
        let action = CounterAction::Increment;
        let bytes = rkyv::to_bytes::<RancorError>(&action).unwrap();
        let archived = rkyv::access::<rkyv::Archived<CounterAction>, RancorError>(&bytes).unwrap();
        println!("✅ rkyv serialization: {:?} -> {} bytes -> {:?}", action, bytes.len(), archived);

        // Increment both counters
        let reaction1 = handler1.act(CounterAction::Increment).await?;
        let reaction2 = handler2.act(CounterAction::Increment).await?;

        println!("✅ Counter 1: {:?}", reaction1);
        println!("✅ Counter 2: {:?}", reaction2);

        // Get values
        let value1 = handler1.act(CounterAction::Get).await?;
        let value2 = handler2.act(CounterAction::Get).await?;

        println!("✅ Final values - Counter 1: {:?}, Counter 2: {:?}", value1, value2);

        // Stop both
        let _ = handler1.act(CounterAction::Stop).await?;
        let _ = handler2.act(CounterAction::Stop).await?;

        println!("✅ Inter-actor communication example completed!");

        Ok(())
    }
}

// ============================================================================
// TESTS
// ============================================================================

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_counter_actor() {
        // Initialize tracing for test output
        let _ = tracing_subscriber::fmt::try_init();

        // Create counter using new handler pattern
        let counter = CounterActor::new("test-counter");
        let handler = CounterHandler::new("test-counter", counter).await.unwrap();

        // Test start
        let reaction = handler.act(CounterAction::Start).await.unwrap();
        assert!(matches!(reaction, CounterReaction::Started));

        // Test increment
        let reaction = handler.act(CounterAction::Increment).await.unwrap();
        assert!(matches!(reaction, CounterReaction::Incremented { new_count: 1 }));

        // Test get
        let reaction = handler.act(CounterAction::Get).await.unwrap();
        assert!(matches!(reaction, CounterReaction::Count { value: 1 }));

        // Test another increment
        let reaction = handler.act(CounterAction::Increment).await.unwrap();
        assert!(matches!(reaction, CounterReaction::Incremented { new_count: 2 }));

        // Test get again
        let reaction = handler.act(CounterAction::Get).await.unwrap();
        assert!(matches!(reaction, CounterReaction::Count { value: 2 }));

        // Test stop
        let reaction = handler.act(CounterAction::Stop).await.unwrap();
        assert!(matches!(reaction, CounterReaction::Stopped));

        println!("✅ All tests passed! TAMTIL MVP is working with rkyv serialization and actors parameter.");
    }

    #[tokio::test]
    async fn test_url_addressing() {
        // Test URL-based actor IDs
        let platform_id = ActorId::new("platform.com");
        let context_id = ActorId::new("platform.com/web/main");
        let actor_id = ActorId::new("platform.com/web/main/counter/user-stats");

        assert_eq!(platform_id.as_str(), "platform.com");
        assert_eq!(context_id.as_str(), "platform.com/web/main");
        assert_eq!(actor_id.as_str(), "platform.com/web/main/counter/user-stats");

        println!("✅ URL-based addressing working correctly!");
    }

    #[tokio::test]
    async fn test_hierarchical_spawning() {
        let _ = tracing_subscriber::fmt::try_init();

        // Create root actor
        let root_actor = CounterActor::new("platform.com");
        let root_handler = CounterHandler::new("platform.com", root_actor).await.unwrap();

        // Start root
        let reaction = root_handler.act(CounterAction::Start).await.unwrap();
        assert!(matches!(reaction, CounterReaction::Started));

        // Spawn child
        let reaction = root_handler.act(CounterAction::Spawn { name: "web".to_string() }).await.unwrap();
        assert!(matches!(reaction, CounterReaction::Spawned { child } if child == "platform.com/web"));

        // Spawn another child
        let reaction = root_handler.act(CounterAction::Spawn { name: "api".to_string() }).await.unwrap();
        assert!(matches!(reaction, CounterReaction::Spawned { child } if child == "platform.com/api"));

        println!("✅ Hierarchical spawning working!");
    }

    #[tokio::test]
    async fn test_rkyv_serialization() {
        // Test that our actions and reactions can be serialized with rkyv
        let action = CounterAction::Increment;
        let bytes = rkyv::to_bytes::<RancorError>(&action).unwrap();
        let archived = rkyv::access::<rkyv::Archived<CounterAction>, RancorError>(&bytes).unwrap();
        assert_eq!(archived, &action);

        let reaction = CounterReaction::Incremented { new_count: 42 };
        let bytes = rkyv::to_bytes::<RancorError>(&reaction).unwrap();
        let archived = rkyv::access::<rkyv::Archived<CounterReaction>, RancorError>(&bytes).unwrap();
        assert_eq!(archived, &reaction);

        println!("✅ rkyv serialization working correctly!");
    }
}
